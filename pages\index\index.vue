<template>
  <page>
    <view slot="content" class="body-main">
      <loading-layer :cData="isLoadingLayer"></loading-layer>
      <view class="top-head-main"></view>
      <!-- #ifdef MP-WEIXIN -->
      <view class="top-prompt" v-if="isTopPrompt" :style="'top: ' + (statusBarHeight + 44) + 'px;'">
        <view class="text-main">
          <text class="text">点击</text>
          <text class="dots">
            <text class="l"></text>
            <text class="m"></text>
            <text class="r"></text>
          </text>
          <text class="text">添加到我的小程序，医患互动、查看科普更方便</text>
        </view>
        <uni-icons class="icon-close" @tap="shutAddTopPrompt()" type="closeempty" size="17" color="#fff" />
      </view>
      <!-- #endif -->
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <image mode="aspectFit" :src="file_ctx + 'static/image/business/hulu-v2/icon-home-logo-ai-new-bg.png'" class="icon-logo"></image>
      </view>
      <view style="padding: 24rpx 32rpx 0">
        <search class="my-search" :hotSearchList="hotSearchList" :fixed="true" top="88" v-model="regForm.search"></search>
      </view>
      <view class="m-main">
        <view class="m-main-body">
          <scroll-refresh
            class="scroll-refresh-main"
            bgColor="rgba(0,0,0,0)"
            :isShowEmptySwitch="false"
            :fixed="false"
            :isAbsolute="false"
            :up="up"
            :down="down"
            @scrollInit="mMainScrollInit"
            @returnFn="mMainReturnFn"
          >
            <region-panel class="myregion-panel" ref="regionPanel" />
            <view class="main-footer">
              <!-- #ifndef MP-ALIPAY -->
              <banner-ads ref="bannerAdsRef" class="banner-ads" :query-params="{ useType: 1 }" height="208rpx"></banner-ads>
              <account-subscribe ref="accountSubscribeRef" class="account-subscribe" />
              <!-- #endif -->
              <view class="list-main">
                <view class="l-main">
                  <tabs-sticky :fontBigger="true" :bdb="false" :overflowX="true" v-model="curIndex" :tabs="tabs" @change="changeTab"></tabs-sticky>
                </view>
                <template v-for="(item, index) in tabs">
                  <view :key="item.id">
                    <!-- 帖子 -->
                    <information-list
                      v-if="(curIndex == 0 && index === 0) || (curIndex == index && item.contentType === 1)"
                      :list="courseList"
                      :index="curIndex"
                      :posts-params="postsParams"
                    ></information-list>
                    <!-- 直播 -->
                    <video-item :list="videolist" v-else-if="curIndex == index && item.contentType === 2"></video-item>
                  </view>
                </template>
              </view>
            </view>
          </scroll-refresh>
        </view>
      </view>
      <disclaimers :updatecount="disclaimersUpdateCount"></disclaimers>
      <!-- 祈福弹窗 -->
      <prayPopup class="prayPopup" v-if="popupParams.length" :popupParams="popupParams" ref="prayPopup" id="prayPopup" @handlePrayPopupClose="handlePrayPopupClose"></prayPopup>
      <!-- 在线客服 -->
      <customerService v-if="isShowConfig" ref="customerService"></customerService>
    </view>
  </page>
</template>

<script>
import { mapState } from 'vuex';
import TabsSticky from '@/components/basics/tabs-sticky-v3';
import StatusBarHeight from '@/components/business/module/status-bar-height/index';
import bannerAds from '@/components/basics/banner-ads/index.vue';
import informationList from './components/information-list';
import env from '@/config/env';
import search from '@/components/basics/search-v1/index.vue';
import videoItem from './components/videoItem/index.vue';
import accountSubscribe from './components/account-subscribe';
import uniIcons from '@/components/uni/uni-icons/uni-icons.vue';
import regionPanel from './components/region-panel';
import { isDomainUrl } from '@/utils/index.js';
import disclaimers from '@/components/basics/disclaimers/index.vue';
import prayPopup from '@/components/basics/pray-popup/index.vue';
import customerService from '@/components/basics/customerService/index.vue';
const launchOptions = uni.getLaunchOptionsSync();
export default {
  components: {
    TabsSticky,
    StatusBarHeight,
    bannerAds,
    informationList,
    search,
    videoItem,
    accountSubscribe,
    uniIcons,
    regionPanel,
    disclaimers,
    prayPopup,
    customerService
  },
  data() {
    return {
      disclaimersUpdateCount: 0,
      videolist: [],
      $common: this.$common,
      $constant: this.$constant,
      file_ctx: this.file_ctx,
      statusBarHeight: 0,
      down: {
        auto: false
      },
      up: {
        auto: false
      },
      regForm: {},
      isLoadingLayer: true,
      storeList: [],
      courseList: [],
      logo: '',
      cityInfo: {},
      info: [],
      modeIndex: -1,
      styleIndex: -1,
      current: 0,
      isTopPrompt: false, // 提示添加到小程序
      mMainScrollObj: {}, // 模块下拉组件初始化
      isOnLoadStatus: false, // 第一次预先加载状态
      curIndex: 0,
      tabs: [{ name: '推荐' }],
      wishingwellData: {},
      officialAccountStyle: '',
      hotSearchList: [], // 热搜列表
      timer: null,
      // 加载时间戳
      timestampLoad: '',
      homePopupTypeObj: null,
      popupParams: [], //弹窗参数
      isShowConfig: false,

      // 防止重复弹窗刷新弹窗
      hasShownNetworkModal: false
    };
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: (state) => state.recordUserInfo, // 当前登录用户信息
      curSelectUserInfo: (state) => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: (state) => state.curSelectUserInfoId, // 当前选中的用户的id
      accountId: (state) => state.accountId
    }),
    ...mapState('system', {
      cityInfoStorage: (state) => state.cityInfoStorage, // 当前定位信息集合
      isOnShow: (state) => state.isOnShow
    }),
    postsParams() {
      return {
        medicineClassifyId: this.curIndex == 0 || !this.tabs[+this.curIndex] ? null : this.tabs[+this.curIndex].id
      };
    }
  },
  onHide() {
    uni.removeStorageSync('initialData');
  },
  onUnload() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
    uni.removeStorageSync('initialData');
  },
  onLoad(paramsObj) {
    const that = this;
    const query = that.$Route.query;
    that.isLoadingLayer = true;
    that.isOnLoadStatus = true;
    that.statusBarHeight = uni.getSystemInfoSync().statusBarHeight; // 获取设备的状态栏高度
    this.getCustomerserviceprofilesQueryNoParametersOne();
    this.searchbuzzwordQueryList();
    this.timer = setInterval(this.searchbuzzwordQueryList, 600000); //每十分钟执行一次 轮询获取搜索词
    that.getMedicineclassifyQueryList();
    this.init();
    that.$nextTick(async () => {
      // #ifdef MP-WEIXIN
      that.isAddTopPrompt();
      wx.showShareMenu({
        withShareTicket: true,
        //设置下方的Menus菜单，才能够让发送给朋友与分享到朋友圈两个按钮可以点击
        menus: ['shareAppMessage', 'shareTimeline']
      });
      // #endif

      that.$common.setKeyVal('system', 'isOnShow', true);
      that.initiData();

      const cachedData = uni.getStorageSync('initialData'); //获取本地缓存 防止弹窗二次请求
      // #ifndef MP-ALIPAY
      this.homePopupTypeObj = this.$common.getKeyVal('user', 'homePopupTypeObj');
      if (this.homePopupTypeObj?.type == 1) {
        this.handleAdvertisementmanagementValidPopAccount('2150284013886636034'); //写死之前绿色弹窗极速咨询
      } else if (this.homePopupTypeObj?.type == 11) {
        this.handleAdvertisementmanagementValidPopAccount('2150314622675714049'); //写死之前辟谣曲光针
      } else {
        if (cachedData) {
          this.handleAdvertisementmanagementValidPopAccount(this.homePopupTypeObj?.id);
        }
      }
      // #endif
    });
  },
  mounted() {},

  onShow() {
    // #ifdef MP-WEIXIN
    this.setTabBarIndex(0);
    if (this.$refs.accountSubscribeRef) this.$refs.accountSubscribeRef.init();
    // #endif

    this.getChatlistPatientQueryList().then(() => {
      this.$ext.common.queryNewsUnread().then((data) => {
        // #ifdef MP-WEIXIN
        Object.keys(data).forEach((index) => {
          this.setTabBarBadge(index, data[index]);
        });
        // #endif
      });
    });
    if (this.isOnLoadStatus) {
      this.initiData();
    }
  },
  methods: {
    // 在线客服
    async getCustomerserviceprofilesQueryNoParametersOne() {
      const res = await this.$api.drugBook.getCustomerserviceprofilesQueryNoParametersOne();
      if (res.data !== '') {
        res.data.enableStatus == 1 && res.data.types.includes('1') ? (this.isShowConfig = true) : (this.isShowConfig = false);
      }
    },
    handleAdvertisementmanagementValidPopAccount(adsId) {
      this.$api.drugBook.advertisementmanagementListValidPopAccount({ accountId: this.accountId, useType: 1, adsId: adsId || '' }).then((res) => {
        if (res.data?.length) {
          // #ifdef MP-ALIPAY
          res.data = res.data.filter((item) => {
            item.managementItemList = item.managementItemList.filter((e) => {
              // 跳转类型:1-静态，2-跳内部页面，3-跳Web地址,4-跳小程序
              switch (+e.jumpType) {
                case 3:
                  return ['open.weixin.qq.com'].every((i) => e.jumpUrl.indexOf(i) === -1);
                case 4:
                  return false;
                default:
                  return true;
              }
            });
            return !this.$validate.isNull(item.managementItemList);
          });
          // #endif

          this.popupParams = res.data;
          this.$nextTick(() => {
            this.$refs.prayPopup?.$refs?.carouselPopups?.open();
          });

          //神策埋点
          // #ifdef MP-WEIXIN
          this.handleClickTrack('PopupExposure');
          // #endif
        }
      });
    },

    // #ifdef MP-WEIXIN
    handleClickTrack(type, data) {
      getApp().globalData.sensors.track(type, {
        page_name: '首页',
        popup_id: 'prayPopup',
        name: '祈福弹窗',
        ...data
      });
    },
    // #endif
    // 每次进首页都会弹窗
    handlePrayPopupClose(type, data) {
      let disclaimersCount = this.$common.getCache('disclaimersCount');
      this.$refs.prayPopup.$refs.carouselPopups.close();
      // #ifdef MP-WEIXIN
      this.handleClickTrack(type, data);
      // #endif
      if (disclaimersCount == '') {
        this.disclaimersUpdateCount += 1;
      }
    },
    // 搜索栏热度词
    async searchbuzzwordQueryList() {
      const res = await this.$api.postmessage.searchbuzzwordQueryList({
        putawayStatus: 1
      });
      this.hotSearchList = [{ word: '搜问题' }, ...res.data];
    },
    async getMedicineclassifyQueryList() {
      const res = await this.$api.medicineclassify.medicineclassifyQueryList({
        classifyAreaType: 1,
        classifyPutawayStatus: 1
      });
      this.tabs = this.tabs.concat(res.data);
    },
    meetingReturnFn(obj, successCallback) {
      const that = this;
      function queryPage(pageNum, pageSize, fn) {
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            // terminalType: 1
            // 活动状态：1-草稿，2-预告，3-直播中，4-直播结束，5-回放，6-下架
            // activityStatus: '2,3,5',
            activityStatusList: [2, 3, 5],
            showType: 1,
            businessType: that.tabs[+that.curIndex].liveBusinessType || 7, // 直播活动
            // orderByActivityStatus:3
            orderByActivityStatus: '5,2,3'
          },
          descs: 'createTime'
        };

        that.$api.cloudClassroom.getMeetingQueryPage(param).then((res) => {
          if (res && res.data.records) {
            let list = res.data.records;
            if (list.length > 0) {
              for (const a in list) {
                const data = list[a];
                data.startTimeText = that.$common.formatDate(new Date(data.startTime), 'yyyy-MM-dd HH:mm:ss');
                data.endTimeText = that.$common.formatDate(new Date(data.endTime), 'yyyy-MM-dd HH:mm:ss');
                data.coverPathsUrl = isDomainUrl(data.coverPaths);
              }
            }
            fn(res.data.records.length > 0 ? res.data.records : []);
          }
        });
      }
      queryPage(obj.pageNum, obj.pageSize, (data) => {
        if (obj.pageNum === 1) {
          that.videolist = [];
        }
        that.videolist = that.videolist.concat(data);
        successCallback(data || []);
      });
    },

    // 获取资讯聊天列表
    getChatlistPatientQueryList() {
      return new Promise((resolve, reject) => {
        const { centerUserId = '' } = this.$common.getKeyVal('user', 'curSelectUserInfo', true) || {};
        if (centerUserId) {
          this.$api.chat
            .chatlistPatientQueryList({ userId: centerUserId })
            .then((res) => {
              this.$common.setKeyVal('chat', 'chatlist', res.data, false);
              resolve();
            })
            .catch(() => {
              resolve();
            });
        } else {
          resolve();
        }
      });
    },

    // #ifdef MP-WEIXIN
    handleClickTrackTab(data) {
      getApp().globalData.sensors.track('OperationClick', {
        page_name: '首页',
        first_operation_name: data?.name,
        operation_floor: '5',
        operation_type: '导航栏',
        is_link_third: data?.is_link_third || '否',
        operation_id: data?.id + '',
        target_url: '',
        position_rank: '11'
      });
    },
    // #endif

    /**
     * 切换标签
     * @param index
     */
    changeTab(index) {
      if (launchOptions.scene === 1154) return this.$uniPlugin.toast('请前往小程序使用完整服务');
      this.curIndex = index;
      const that = this;
      // #ifdef MP-WEIXIN
      this.handleClickTrackTab(this.tabs[index]);
      // #endif
      that.mMainScrollObj.optUp.page.num = 1;
      that.mMainScrollObj.optUp.page.size = 10;
      // that.storeList = []
      that.init();
    },
    navtoGo(url = '', obj = {}) {
      if (url == 'zixun') {
        const { centerUserId = '' } = this.curSelectUserInfo || {};
        if (!centerUserId) {
          this.$navto.push('Login');
          return;
        }
        this.$uniPlugin.loading('加载中', true);
        this.$api.order
          .orderInitiatorUserCheck({ userId: centerUserId, tenantId: this.$common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId })
          .then((res) => {
            this.$uniPlugin.hideLoading();
            this.$navto.push('Chat', res.data);
          })
          .catch(() => {
            this.$uniPlugin.hideLoading();
          });

        return;
      }
      this.$navto.push(url, obj);
    },
    initiData() {
      const that = this;
      that.$nextTick(() => {
        let cityInfoStorage = that.$common.getKeyVal('system', 'cityInfoStorage', true);
        if (typeof cityInfoStorage === 'string' && that.$validate.isNull(cityInfoStorage)) {
          cityInfoStorage = {};
        } else if (typeof cityInfoStorage === 'string' && !that.$validate.isNull(cityInfoStorage)) {
          cityInfoStorage = JSON.parse(cityInfoStorage);
        }
        if (!that.$validate.isNull(cityInfoStorage)) {
          if (typeof cityInfoStorage === 'object' && !that.$validate.isNull(cityInfoStorage)) {
            that.cityInfo = cityInfoStorage;
          } else {
            that.cityInfo = {};
          }
        } else {
          that.cityInfo = {};
        }
        if (that.isOnShow) {
          that.$common.setKeyVal('system', 'isOnShow', false);
        }
      });
    },
    init() {
      this.$nextTick(() => {
        this.mMainScrollObj.triggerDownScroll();
      });
    },
    // 判断提示添加小程序业务逻辑
    isAddTopPrompt() {
      const that = this;
      that.isTopPrompt = false;
      // 判断是否第一次进入小程序
      const isSmallProgram = that.$common.getKeyVal('user', 'isSmallProgram', true);
      if (isSmallProgram === 1) {
        that.isTopPrompt = false;
      } else {
        that.isTopPrompt = true;
        setTimeout(function () {
          that.$common.setKeyVal('user', 'isSmallProgram', 1, true);
          that.isTopPrompt = false;
        }, 10000);
      }
    },
    // 关闭提示添加小程序业务逻辑
    shutAddTopPrompt() {
      this.isTopPrompt = false;
      this.$common.setKeyVal('user', 'isSmallProgram', 1, true);
    },
    mMainScrollInit(scroll) {
      scroll.optUp.page.num = 1;
      scroll.optUp.page.size = 3;
      this.mMainScrollObj = scroll;
    },
    mMainReturnFn(obj) {
      console.log('mMainReturnFn');
      const that = this;
      that.methodsCollection(obj).then(() => {
        that.isLoadingLayer = false;
      });
      const curTab = that.tabs[+that.curIndex];
      if (that.curIndex === 0 || curTab.contentType === 1) {
        that.courseReturnFn(obj, (data) => {
          obj.successCallback && obj.successCallback(data || []);
        });
      } else if (curTab.contentType === 2) {
        that.meetingReturnFn(obj, (data) => {
          obj.successCallback && obj.successCallback(data || []);
        });
      }
    },
    // 下拉刷新方法业务逻辑集合
    async methodsCollection(obj) {
      const that = this;
      if (obj.pageNum === 1) {
        await this.allInit(obj); // 运用板块方法集合所有組件初始化
      }
    },
    getData() {
      let today = new Date();
      //日期
      let DD = String(today.getDate()).padStart(2, '0'); // 获取日
      let MM = String(today.getMonth() + 1).padStart(2, '0'); //获取月份，1 月为 0
      let yyyy = today.getFullYear(); // 获取年
      // 时间
      let hh = String(today.getHours()).padStart(2, '0'); //获取当前小时数(0-23)
      let mm = String(today.getMinutes()).padStart(2, '0'); //获取当前分钟数(0-59)
      let ss = String(today.getSeconds()).padStart(2, '0'); //获取当前秒数(0-59)
      today = yyyy + '-' + MM + '-' + DD + ' ' + hh + ':' + mm + ':' + ss;
      return today;
    },
    courseReturnFn(obj, successCallback) {
      const that = this;
      function queryPage(pageNum, pageSize, fn) {
        console.log('pageNum', pageNum);
        // 页面为一 即可默认页面刷新 刷新时重新赋值时间戳
        if (pageNum === 1) that.timestampLoad = that.getData();
        const param = {
          current: pageNum,
          size: pageSize,
          condition: {
            // terminalType: 1
            currentDate: that.timestampLoad,
            accountId: that.accountId,
            medicineClassifyId: that.curIndex == 0 ? null : that.tabs[+that.curIndex].id
          }
        };
        that.$ext.community.postmessageQueryRecommendPage(param).then((res) => {
          if (res && res.data.records) {
            let list = res.data.records;
            if (list.length > 0) {
              for (const a in list) {
                const data = list[a];
              }
            }
            fn(res.data.records.length > 0 ? res.data.records : []);
          }
        });
      }
      if (that.accountId) {
        queryPage(obj.pageNum, obj.pageSize, (data) => {
          if (obj.pageNum === 1) {
            that.courseList = [];
          }
          that.courseList = that.courseList.concat(data);
          that.courseList = that.courseList.map((item) => {
            try {
              item.topicIdsArr = (item.topicIds && JSON.parse(item.topicIds)) || [];
            } catch (e) {
              console.log('JSON解析错误:', e);
            }
            return {
              ...item
            };
          });
          successCallback(data || []);
        });
      }
    },

    // 所有組件初始化
    async allInit(obj) {
      const that = this;
      await Promise.all([
        // #ifndef MP-ALIPAY
        that.$refs.bannerAdsRef.init(),
        // #endif
        that.$refs.regionPanel.init()
      ]);
    }
  }
};
</script>

<style lang="scss">
/deep/.my-search {
  .search {
    // padding: 24rpx 0 16rpx !important;
    padding: 0 !important;
  }
}
.main-footer {
  box-sizing: border-box;
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  display: inline-block;
  position: relative;
  width: 100%;
  /deep/.banner-ads {
    .banner-main {
      padding: 0 32rpx;
    }
  }
  /deep/.account-subscribe {
    .wrap {
      margin: 0 32rpx;
    }
  }
}
.body-main {
  background: #ffffff;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.top-head-main {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  height: 604rpx;
  background-image: url($imgUrl + '/business/hulu-v2/icon-home-ai-new-bg.png');
  background-size: 100%;
  // background-position: center;
  background-repeat: no-repeat;
}
.top-prompt {
  position: fixed;
  left: 30rpx;
  display: flex;
  align-items: center;
  height: 78rpx;
  line-height: 78rpx;
  width: 690rpx;
  background: rgba(0, 0, 0, 0.7);
  padding: 0 14rpx 0 20rpx;
  z-index: 9999;
  border-radius: 18rpx;
  box-sizing: border-box;
  &::before {
    content: '';
    position: absolute;
    top: -12rpx;
    right: 92rpx;
    width: 0;
    height: 0;
    border-bottom: 12rpx solid rgba(0, 0, 0, 0.7);
    border-left: 14rpx solid transparent;
    border-right: 14rpx solid transparent;
  }
  .text-main {
    flex: 1;
    display: inline-block;
    vertical-align: middle;
    width: calc(100% - 50upx);
    color: #fff;
    font-size: 24upx;
    @include ellipsis(1);
    margin: 16rpx 12rpx 16rpx 0;
    .text {
      font-size: 24rpx;
      color: #ffffff;
      line-height: 34rpx;
    }
    .dots {
      margin: 0 6rpx;
      .l,
      .m,
      .r {
        display: inline-block;
        vertical-align: middle;
        width: 10upx;
        height: 10upx;
        @include rounded(50%);
        background: #fff;
      }
      .l {
        margin-right: 4upx;
      }
      .m {
        width: 14upx;
        height: 14upx;
      }
      .r {
        margin-left: 4upx;
      }
    }
  }
}
.top-nav {
  position: relative;
  display: flex;
  align-items: center;
  height: 44px;
  line-height: 44px;
  margin-top: 44px;
  // #ifdef MP-ALIPAY
  padding: 0 88rpx;
  // #endif
  // #ifndef MP-ALIPAY
  padding: 0 32rpx;
  // #endif
  .icon-logo {
    width: 204rpx;
    height: 54rpx;
  }
}
.m-main {
  position: relative;
  height: 100%;
  // #ifdef MP-WEIXIN
  padding-bottom: calc(56px + env(safe-area-inset-bottom));
  // #endif
  .m-main-body {
    height: 100%;
    .scroll-refresh-main {
      height: 100%;
    }
  }
}
/deep/ .myregion-panel {
  .panel-wrapper {
    padding: 40rpx 32rpx 0 !important;
  }
}
.list-main {
  background: #ffffff;
  position: relative;
  margin-top: 32rpx;
  .l-main {
    position: sticky;
    top: 0;
    z-index: 99;
    padding: 0 32rpx;
    background-color: transparent;
    /deep/.tabs-sticky {
      padding: 16rpx 0;
      .tabs-sticky-body {
        padding: 0;
        .tab {
          text {
            padding: 0;
          }
        }
      }
    }
  }
}
.prayPopup {
  /deep/.my-uni-popup {
    .uni-popup {
      .uni-popup__wrapper {
        .uni-popup__wrapper-box {
          max-width: none;
        }
      }
    }
  }
}
</style>
