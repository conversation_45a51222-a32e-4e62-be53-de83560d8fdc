<template>
  <view class="model-container">
    <uniPopup :zIndex='99' type="bottom" ref='uniPopup'>
      <view class="confirm">
        <!-- 标题 -->
        <view class="confirmTitle">
          <text>切换服务商</text>
          <image class="iconClose" @click="close" :src="iconClose" mode=""></image>
        </view>
        <!-- 服务商列表 -->
        <view class="providerList">
          <view class="providerItem" @click="changeProvider(item)" v-for="item in providerList" :key="item.id" :class="{'providerItemActive':item.id == currentProviderId}">
            <image class="providerAvatar" :src="file_ctx + item.logo" mode="round"></image>
            <view class="providerName">{{item.providerName}}</view>
          </view>
        </view>
        <!-- 确认按钮 -->
        <view class="actionsConfirm" @click="trigger">确认切换</view>
      </view>
    </uniPopup>
  </view>
</template>

<script>
  import common from '@/common/util/main'
  import { mapState } from "vuex";
  import uniPopup from '@/components/uni/uni-popup/uni-popup.vue'
  import serverOptions from '@/config/env/options'
  export default{
    components: {
        uniPopup,
    },
    props:{
    },
    watch: {

    },
    data(){
      return {
        file_ctx: this.file_ctx,
        iconClose: this.$static_ctx + "image/business/hulu-v2/close-white.png",
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        providerList:[],
        currentProviderId:'',
        userId:''
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        fansRecord: (state) => state.fansRecord,
      }),

    },
    async mounted() {
      let {data} = await this.$api.accompanyDoctor.getAccompanyproviderAll()
      console.log('providerList',data);
      this.providerList = data;
      const userId = serverOptions.getUserId(this);
      if(!userId) return
      let {data:id} = await this.$api.accompanyDoctor.accompanyproviderUserProvider({userId})
      this.currentProviderId = id;
      this.userId = userId;
    },
    methods:{
      open(){
        this.$refs.uniPopup.open()
      },
      changeProvider(item){
        this.currentProviderId = item.id;
      },
      // 切换当前用户服务商
      async trigger(){
        await this.$api.accompanyDoctor.changeProvider({
          userId: this.userId,
          providerId: this.currentProviderId,
        })
        uni.reLaunch({
          url: 'modules/accompany-doctor/home/<USER>' + this.currentProviderId
        })
      }
    }
  }
</script>

<style lang="scss">
  .iconRightArrow{
    width: 32rpx;
    height: 32rpx;
  }

  .confirm{
    width: 100vw;
    background: #F4F6FA;
    padding: 32rpx;
    box-sizing: border-box;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    position: fixed;
    bottom: 130rpx;
    .confirmTitle{
      font-weight: 500;
      font-size: 32rpx;
      color: #1D2029;
      text-align: center;
      position: relative;
      .iconClose{
        width: 32rpx;
        height: 32rpx;
        position: absolute;
        right: 0;
        top: 6rpx;
      }
    }
    .actionsConfirm{
      width: 686rpx;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      text-align: center;
      line-height: 88rpx;
      margin-top: 30rpx;
    }
  }
  .providerList{
    margin-top: 12rpx;
    width: 100%;
    padding: 20rpx;
    border-radius: 10rpx;
    background-color: white;
    box-sizing: border-box;
    height: 600rpx;
    overflow: scroll;
    .providerItem{
      width: 100%;
      height: 100rpx;
      border-bottom: 1rpx solid #F4F6FA;
      display: flex;
      align-items: center;
      border-radius: 10rpx;
      .providerName{
        font-size: 28rpx;
        line-height: 42rpx;
        color: #1D2029;
        margin-left: 20rpx;
      }
      .providerAvatar{
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        margin-left: 20rpx;
      }
    }
  }
  .providerItemActive{
    background-color: #ababab;
  }
</style>
