<template>
  <view class="pages">

    <formList @emitInput='emitInput' @previewImage='previewImage' @closeImgage='closeImgage' @emitChange='emit' :serverMap='currentOptions'></formList>
    <view class="bottomPlaceholder"></view>
    <view class="bottomBtn">
      <view class="clockIn" @click="confirmActions">创建订单</view>
    </view>
    <!-- 弹窗模块 -->
    <changeServer ref="selectChangeServer" :isISP='true' :isFromCategory='true' :classifyId='selectedCategoryId' @change='changeExchange' @selectServer='selectServer' :openFlag='openServerFlag' ></changeServer>
    <view class="lineHide">
      <selectData ref="selectHospital" placeholder="输入医院名称，模糊匹配搜索"  :localdata="hospitalQuery" popup-title="请选择就诊医院" @change="onchangeHospital"></selectData>
    </view>
    <view class="lineHide">
      <timePicker ref="selectTime" type="datetimerange" @change="onchangeTime" />
    </view>
    <view class="lineHide">
      <dataPicker ref="selectCity" :localdata="provinceMap" popup-title="请选择就诊城市" @change="onchangeCity"></dataPicker>
    </view>
    <view class="lineHide">
      <dataPicker ref="selectAccompanyemployee" :localdata="accompanyemployeeMap" popup-title="请选择陪诊师" @change="onchangeEmployee"></dataPicker>
    </view>
    <view class="lineHide">
      <dataPicker ref="selectInitiateProviderId" :localdata="initiateProviderIdMap" popup-title="请选择下单服务商" @change="onchangeInitiateProviderId"></dataPicker>
    </view>
    <view class="lineHide">
      <dataPicker ref="selectProviderId" :localdata="providerIdMap" popup-title="请选择接单服务商" @change="onchangeProviderId"></dataPicker>
    </view>
    <view class="lineHide">
      <selectData ref="selectDeptName" placeholder="输入科室名称，模糊匹配搜索" :localdata="deptNameQuery" popup-title="请选择就诊科室" @change="onchangeDeptName"></selectData>
    </view>
    <view class="lineHide">
      <selectData ref="selectDoctorName" placeholder="输入医生名称，模糊匹配搜索" :localdata="doctorNameQuery" popup-title="请选择就诊医生" @change="onchangeDoctorName"></selectData>
    </view>
    <view class="lineHide">
      <title-img :config="{}" ref="upDataImage" @returnFn="imgReturnFn"></title-img>
    </view>
    <!-- 就诊人选择弹窗 -->
    <patientSelectModal :queryAll="true" :provinceValue='provinceValue' @selectPatient="selectPatient" ref="patientSelectModal"></patientSelectModal>
  </view>
</template>

<script>
  import { mapState } from "vuex";
  import common from '@/common/util/main'
  import formList from './components/formList.vue'
  import timePicker from '../../components/uni-datetime-picker/uni-datetime-picker'
  import dataPicker from '../../components/uni-data-picker/uni-data-picker.vue'
  import changeServer from '../../components/changeServer'
  import selectData from '../../components/select-data.vue'
  import TitleImg from "@/components/business/module/title-img/index.vue"
  import serverOptions from '@/config/env/options'
  import patientSelectModal from '../../components/patientSelectModal.vue'

  export default{
    components: {
        changeServer,
        timePicker,
        dataPicker,
        formList,
        TitleImg,
        selectData,
        patientSelectModal,
    },
    async mounted() {
      const userId = serverOptions.getUserId(this);
      console.log('userIduserId',userId);
      let source = serverOptions.source;
      this.setForm('platformOrder','hidden',source === 1);
      this.setForm('platformOrder','required',source === 2);
      let provinceValue = (await this.$api.providerManagement.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
      this.provinceValue = provinceValue;
      if(!userId){
        uni.showToast({title:'请先登录',icon:'none'})
        return;
      }
      let {data:id} = await this.$api.providerManagement.accompanyproviderUserProvider({userId})
      this.providerId = id;
      let {data} = await this.$api.providerManagement.getAccompanyproviderAll();
      // 获取城市列表
      this.provinceMap = await this.accompanyproviderQueryPage();
      let {data:{records}} = await this.$api.providerManagement.getAccompanyemployeePage({
        size: 10000,
        condition: {
          providerId: this.providerId,
          auditStatus: 2
        }
      });
      records.map(e=>{
              e.text = e.username
              e.value = e.id
      })
      this.accompanyemployeeMap = records;
    },
    onShow(){
      uni.$emit('onShow');
    },
    data(){
      return {
        openServerFlag:false,
        currentOptions:[
          {required:true,title:'就诊人姓名',value:'',text:'',noValue:'请输入',valueType:'bookName',textType:'bookName',textType:'bookName'},
          {required:true,title:'就诊人电话',value:'',text:'',noValue:'请输入',valueType:'bookPhone',textType:'bookPhone',type:'input'},
          {required:true,title:'就诊城市',value:'',text:'',noValue:'请选择',valueType:'provinceCity',textType:'provinceCity',classTimeMap:['province','city','county']},
          {required:true,title:'就诊医院',value:'',text:'',noValue:'请选择',valueType:'hospitalName',textType:'hospitalName'},
          {required:false,title:'医院科室',value:'',text:'',noValue:'请选择',valueType:'deptName',textType:'deptName'},
          {required:false,title:'就诊医生',value:'',text:'',noValue:'请选择',valueType:'doctorName',textType:'doctorName'},
          {required:true,title:'陪诊时间',value:'',text:'',noValue:'开始时间-结束时间',valueType:'selectTime',textType:'selectTime',classTimeMap:['startTime','endTime']},
          {required:false,title:'补充内容',value:[],text:'',valueType:'backupImg',textType:'backupImg',type:'image'},
          // {required:true,title:'下单服务商',valueType:'initiateProviderId',value:'',text:'',textType:'initiateProviderId',showSource:1},
          {required:true,title:'接单服务商',valueType:'providerId',value:'',text:'',textType:'providerId',showSource:1},
          {required:true,title:'服务分类',value:'',text:'',noValue:'请选择',valueType:'classifyId',textType:'classifyId'},
          {required:true,title:'服务',value:'',text:'',noValue:'请选择',valueType:'serviceId',textType:'serviceName'},
          {required:true,title:'服务价格',value:'',text:'',noValue:'请输入',valueType:'price',textType:'price', type: 'price-input',inputType: 'number'},
          {required:false,title:'备注',value:'',text:'',noValue:'请选择',valueType:'remark',textType:'remark',type:'textarea'},
          {required:true,title:'就诊医院',value:'',text:'',noValue:'请选择',valueType:'hospitalId',textType:'hospitalId',hidden:true},
          {required:true,title:'是否为平台单',valueType:'platformOrder',value:0,textType:'platformOrder',type:'switch'},
        ],
        hospitalQuery: [],
        provinceMap: [],
        accompanyemployeeMap: [],
        providerIdMap: [],  // 接单服务商列表
        initiateProviderIdMap: [],  // 下单服务商列表
        deptNameQuery: [],
        doctorNameQuery: [],
        dto: {},
        provinceValue: {},
        allProvidersData: [],  // 存储所有服务商原始数据
        selectedProviderId: null,  // 存储用户选择的接单服务商ID
        categoryList: [], // 存储服务分类列表
        selectedCategoryId: '', // 存储用户选择的服务分类ID
      }
    },
    computed: {
      ...mapState("user", {
        accountId: (state) => state.accountId,
        codeUserInfo: state => state.codeUserInfo,
        fansRecord: (state) => state.fansRecord,
      }),
    },
    methods:{
      selectPatient({name,phone,sex,idcard}){
        console.log('{name,phone,sex,idcard}',{name,phone,sex,idcard});

        this.setForm('bookName','text',name);
        this.setForm('bookName','value',name);
        this.setForm('bookPhone','text',phone);
        this.setForm('bookPhone','value',phone);
        console.log('currentOptions',this.currentOptions);

        this.dto = {name,phone,sex,idcard}
      },
      async accompanyproviderQueryPage(){
        // 判断当前是否是平台
        let queryOptions;
        if(serverOptions.source === 1){
          queryOptions = (await this.$api.providerManagement.getAccompanyproviderAll()).data
        }else{
          queryOptions = [(await this.$api.providerManagement.accompanyproviderQueryOne({id:serverOptions.providerId})).data]
        }
        let cityMap = this.getCityMap(queryOptions)
        return [...new Set(cityMap)].filter(e=>e);
      },
      getCityMap(AccompanyproviderAll){
          return AccompanyproviderAll.reduce((acc, {province, city}) => {
              let provinceMap = province.split(',');
              let cityMap = city.split('$');
              provinceMap.map((provinceItem,index)=>{
              let currentCityMap = cityMap[index].split(',').filter(e=>e);
              let prov = acc.find(p => p.value === provinceItem);
              if (!prov) {
                acc.push(prov = {text: provinceItem, value: provinceItem, children: []});
              }
              prov.children.push(...currentCityMap)
              })
              return acc;
          }, []).map(e=>{
              e.children = [...new Set(e.children)];
              e.children = e.children.map(e=>({text:e,value:e}))
              return e;
          });
      },
      getForm(itemName){
        return this.currentOptions.filter(e=>e.textType === itemName)[0] || {}
      },
      setForm(itemName,className,value){
        this.currentOptions.map(e=>{
          if(e.textType === itemName){
            this.$set(e,className,value)
          }
        })
      },
      changeExchange(flag){
        this.openServerFlag = flag
      },
      selectServer(options){
        this.setForm('serviceName','text',options.serviceName);
        this.setForm('serviceName','value',options.id);
        this.setForm('price', 'value', options.price / 100) // 转换为元
        this.setForm('price', 'text', options.price / 100)
        // 如果服务所属的分类信息可用，也更新分类信息
        if(options.classifyId && options.classifyName) {
          this.selectedCategoryId = options.classifyId;
          this.setForm('classifyId', 'text', options.classifyName);
          this.setForm('classifyId', 'value', options.classifyId);
        }
      },
      onchangeHospital({detail:{value}}){
        this.setForm('hospitalName','text',value[0].text);
        this.setForm('hospitalName','value',value[0].text);
        this.setForm('hospitalId','value',value[0].value);
        this.setForm('deptName','text','');
        this.setForm('deptName','value','');
        value[0].value && this.getdeptName(value[0].value);
      },
      onchangeCity({detail:{value}}){
        this.setForm('provinceCity','text',value.map(e=>e.text).join('~'));
        this.setForm('provinceCity','value',value.map(e=>e.value));
        this.getHospital({pname:value[0].value,cityname:value[1].value});
        this.onchangeHospital({detail:{value:[{text:'',value:''}]}});

        // 清空当前选择的服务商
        this.setForm('initiateProviderId','text','');
        this.setForm('initiateProviderId','value','');
        this.setForm('providerId','text','');
        this.setForm('providerId','value','');

        // 清空专门存储的接单服务商ID
        this.selectedProviderId = null;

        // 请求服务商数据（不再限制只有平台小程序）
        this.providerIdMap = []; // 先清空现有数据
        this.initiateProviderIdMap = []; // 清空下单服务商列表
        this.allProvidersData = []; // 清空原始数据
        this.getProviderId(value[1].value);
      },
      onchangeDeptName({detail:{value}}){
        this.setForm('deptName','text',value[0].text);
        this.setForm('deptName','value',value[0].value);
        value[0].value && this.getDoctorName({deptId:value[0].value,hospitalId:this.getForm('hospitalId').value});
      },
      onchangeDoctorName({detail:{value}}){
        this.setForm('doctorName','text',value[0].text);
        this.setForm('doctorName','value',value[0].text);
      },
      onchangeEmployee({detail:{value}}){
        this.setForm('employeeName','text',value[0].text);
        this.setForm('employeeName','value',value[0].value);
      },
      onchangeInitiateProviderId({detail:{value}}) {
        this.setForm('initiateProviderId', 'text', value[0].text);
        this.setForm('initiateProviderId', 'value', value[0].value);

        // 更新接单服务商列表，排除当前选中的下单服务商
        this.updateProviderLists();

        // 检查是否与当前接单服务商相同
        const currentProviderId = this.getForm('p roviderId').value;
        if(currentProviderId === value[0].value) {
          // 清空接单服务商
          this.setForm('providerId', 'text', '');
          this.setForm('providerId', 'value', '');
          uni.showToast({
            title: '下单服务商和接单服务商不能相同',
            icon: 'none'
          });
        }
      },
      onchangeProviderId({detail:{value}}) {
        this.setForm('providerId', 'text', value[0].text);
        this.setForm('providerId', 'value', value[0].value);
        console.log('value[0].value', value[0].value);

        // 存储用户选择的接单服务商ID
        this.selectedProviderId = value[0].value;

        // 更新下单服务商列表，排除当前选中的接单服务商
        this.updateProviderLists();

        // 检查是否与当前下单服务商相同
        const currentInitiateId = this.getForm('initiateProviderId').value;
        if(currentInitiateId === value[0].value) {
          // 清空下单服务商
          this.setForm('initiateProviderId', 'text', '');
          this.setForm('initiateProviderId', 'value', '');
          uni.showToast({
            title: '下单服务商和接单服务商不能相同',
            icon: 'none'
          });
        }

        // 如果是平台端，则在切换服务商时清空分类和服务
        if (serverOptions.source === 1) {
          this.setForm('classifyId', 'text', '');
          this.setForm('classifyId', 'value', '');
          this.selectedCategoryId = '';

          this.selectServer({id: '', serviceName: ''});

          // 获取该服务商的分类列表
          this.getCategoryList(value[0].value);
        }

        // 清空服务（服务选择依赖于接单服务商）
        this.selectServer({id: '', serviceName: ''});

        // 先重置changeServer组件的状态
        if (this.$refs.selectChangeServer) {
          // 重置组件状态并设置lastProviderId
          this.$refs.selectChangeServer.lastProviderId = value[0].value;
          this.$refs.selectChangeServer.categoryList = [];
          this.$refs.selectChangeServer.currentCategoryId = '';
          this.$refs.selectChangeServer.currentServices = [];
        }

        // 打开服务选择器弹窗
        this.openServerFlag = true;

        // 使用$nextTick确保弹窗已打开再调用getServerData
        this.$nextTick(() => {
          if (this.$refs.selectChangeServer) {
            this.$refs.selectChangeServer.getServerData(true, {providerId: value[0].value});
          }
        });
      },
      onchangeTime(value){
        this.setForm('selectTime','text',value.join('~'));
        this.setForm('selectTime','value',value);
      },
      emitInput({ type, res }) {
        // 处理价格字段的特殊验证
        if (type === 'price') {
          const inputValue = res.detail.value;
          // 验证数字有效性
          if (inputValue === '' || isNaN(inputValue)) {
            uni.showToast({
              title: '请输入有效的价格数字',
              icon: 'none'
            });
            return; // 验证失败不更新值
          }
          const numericValue = parseFloat(inputValue);
          this.setForm(type, 'text', numericValue);
          this.setForm(type, 'value', numericValue);
          return;
        }
        this.setForm(type, 'text', res.detail.value);
        this.setForm(type, 'value', res.detail.value);
        console.log('currentOptions',this.currentOptions);

      },
      imgReturnFn(imageObj){
        let {value:oldValue} = this.getForm('backupImg');
        let newValue = imageObj.map(e=>e.filePath);
        oldValue.forEach((e,index)=>{
          if(newValue.indexOf(e) === -1){
            oldValue = oldValue.slice(index)
          }
        })
        console.log('imageObj',imageObj);
        this.setForm('backupImg','value',imageObj.map(e=>e.filePath));
      },
      previewImage(urls){
        console.log('urls',urls);
        uni.previewImage({urls:[urls]});
      },
      closeImgage(index){
        this.$refs.upDataImage.del(index)
      },
      emit(type){
        console.log('触发',type);
        switch (type){
          case 'serviceName':
          if(serverOptions.source === 1 && !this.getForm('provinceCity').text) return uni.showToast({
              title:'请先选择接单服务商',
              icon:'none'
            })
            // 打开服务选择弹窗并传递必要参数
            this.openServerFlag = true;

            // 如果是平台端，需要传递服务商ID
            if(serverOptions.source === 1) {
              this.$nextTick(() => {
                // 确保弹窗已打开后再调用getServerData
                const providerId = this.getForm('providerId').value;
                if(providerId) {
                  this.$refs.selectChangeServer.getServerData(true, {providerId});
                }
              });
            }
            break;
          case 'classifyId':
            // 直接打开服务选择弹窗，与serviceName相同的处理方式
            if(serverOptions.source === 1 && !this.getForm('providerId').value) return uni.showToast({
              title:'请先选择接单服务商',
              icon:'none'
            })
            // 打开服务选择弹窗
            this.openServerFlag = true;
            this.$nextTick(() => {
              // 平台端需要传递服务商ID
              if(serverOptions.source === 1) {
                const providerId = this.getForm('providerId').value;
                if(providerId) {
                  this.$refs.selectChangeServer.getServerData(true, {providerId});
                }
              }
            });
            break;
          case 'hospitalName':
            if(this.hospitalQuery.length === 0) return uni.showToast({
              title:this.getForm('provinceCity').text ? '当前城市未录入医院数据' : '请先选择城市',
              icon:'none'
            })
            this.$refs.selectHospital.show()
            break;
          case 'selectTime':
            this.$refs.selectTime.show()
            break;
          case 'provinceCity':
            this.$refs.selectCity.show()
            break;
          case 'bookName':
            this.$refs.patientSelectModal.showPatientSelect();
            break;
          case 'backupImg':
            this.$refs.upDataImage.uploadImage()
            break;
            break;
          case 'modeName':
            this.$refs.selectmode.show()
            break;
          case 'employeeName':
            this.$refs.selectAccompanyemployee.show()
            break;
          case 'initiateProviderId':
            if(!this.getForm('provinceCity').text) return uni.showToast({
              title: '请先选择城市',
              icon: 'none'
            });

            // 确保有服务商数据
            if(!this.initiateProviderIdMap || this.initiateProviderIdMap.length === 0) {
              const cityValue = this.getForm('provinceCity').value;
              if(cityValue && cityValue.length > 1) {
                this.getProviderId(cityValue[1]);
                uni.showToast({
                  title: '正在加载服务商数据',
                  icon: 'none'
                });
                return;
              }
            }

            // 检查接单服务商是否已选择
            const providerId = this.getForm('providerId').value;

            if(this.initiateProviderIdMap && this.initiateProviderIdMap.length > 0) {
              // 如果有选择了接单服务商，确保下单服务商列表已正确过滤
              if(providerId) {
                this.updateProviderLists();
              }
              this.$refs.selectInitiateProviderId.show();
            } else {
              uni.showToast({
                title: '当前城市没有可用的服务商',
                icon: 'none'
              });
            }
            break;
          case 'providerId':
            if(!this.getForm('provinceCity').text) return uni.showToast({
              title: '请先选择城市',
              icon: 'none'
            });

            // 确保有服务商数据
            if(!this.providerIdMap || this.providerIdMap.length === 0) {
              const cityValue = this.getForm('provinceCity').value;
              if(cityValue && cityValue.length > 1) {
                this.getProviderId(cityValue[1]);
                uni.showToast({
                  title: '正在加载服务商数据',
                  icon: 'none'
                });
                return;
              }
            }

            // 检查下单服务商是否已选择
            const initiateProviderId = this.getForm('initiateProviderId').value;

            if(this.providerIdMap && this.providerIdMap.length > 0) {
              // 如果有选择了下单服务商，确保接单服务商列表已正确过滤
              if(initiateProviderId) {
                this.updateProviderLists();
              }
              this.$refs.selectProviderId.show();
            } else {
              uni.showToast({
                title: '当前城市没有可用的服务商',
                icon: 'none'
              });
            }
            break;
          // 就诊科室
          case 'deptName':
          if(!this.getForm('hospitalName').text) return uni.showToast({
            title:'请先选择就诊医院',
            icon:'none'
          })
          this.$refs.selectDeptName.show()
          break;
          // 就诊医生
          case 'doctorName':
          if(!this.getForm('deptName').text) return uni.showToast({
            title:'请先选择就诊科室',
            icon:'none'
          })
            this.$refs.selectDoctorName.show()
            break;
          default:
            break;
        }
      },
      async confirmActions() {
        // 配置不需要校验的字段
        const excludeFields = ['backupImg', 'remark', 'deptName', 'doctorName'];

        // 获取需要校验的必填字段（排除指定字段）
        const requiredFields = this.currentOptions.filter(e =>
          e.required &&
          !excludeFields.includes(e.valueType) &&
          (!e.showSource || e.showSource === serverOptions.source)
        );

        // 校验必填字段
        const missingField = requiredFields.find(e =>
          e.value === '' ||
          (Array.isArray(e.value) && e.value.length === 0) ||
          (e.valueType === 'price' && isNaN(e.value))
        );

        if (missingField) {
          let msg = `${missingField.title}不能为空`;
          if (missingField.valueType === 'price') {
            msg = isNaN(missingField.value) ? '服务价格必须为有效数字' : '服务价格必须大于0元';
          }
          uni.showToast({ title: msg, icon: 'none' });
          return;
        }
        // 价格单独校验
        const priceField = this.currentOptions.find(e => e.valueType === 'price');
        if (priceField?.value <= 0) {
          uni.showToast({ title: '服务价格必须大于0元', icon: 'none' });
          return;
        }

        let queryOptions = {
          userId: this.codeUserInfo.id,
          accountId: this.accountId,
          source: serverOptions.source,
          needManual:false,
          dto: this.dto,
          ...this.currentOptions.reduce((nex, current) => {
            if (current.showSource && current.showSource !== serverOptions.source) return nex;
              // 特殊字段处理
              if (current.valueType === 'price') {
                nex[current.valueType] = Number(current.value) * 100; // 转换为分
                return nex;
              }
              if (current.classTimeMap) {
                current.classTimeMap.forEach((e, index) => nex[e] = current.value[index]);
                return nex;
              }
              if (current.type === 'image') {
                nex[current.valueType] = current.value.join(',');
                return nex;
              }
              if (current.valueType === 'deptName') {
                nex[current.valueType] = current.text || ''; // 允许空值
                return nex;
              }
              // 处理providerId字段，确保使用选择的值
              if (current.valueType === 'providerId') {
                nex[current.valueType] = this.selectedProviderId || current.value || this.providerId;
                return nex;
              }
              // 添加classifyId字段，确保服务分类被包含
              if (current.valueType === 'classifyId') {
                nex[current.valueType] = this.selectedCategoryId || current.value;
                return nex;
              }

              nex[current.valueType] = current.value;
              return nex;
            }, {})
          };

          // 强制设置providerId为选择的值
          queryOptions.providerId = this.selectedProviderId || this.providerId;

          // 打印原始queryOptions
          console.log('完整的queryOptions:', JSON.stringify(queryOptions));

          // 确保condition对象中的providerId也被设置为正确的值
          if (!queryOptions.condition) {
            queryOptions.condition = {};
          }
          queryOptions.condition.providerId = this.selectedProviderId || this.providerId;
          // 平台创单默认为云陪诊模式
          queryOptions.store = 1;
          try {
            // 使用修改后的参数调用API
            const result = await this.$api.providerManagement.accompanybook(queryOptions);
            console.log('订单提交成功，返回结果:', result);

            this.$emit('finish');
            uni.showToast({
              title: '订单创建成功',
              icon: 'none'
            });
            setTimeout(() => uni.navigateBack(), 500);
          } catch (error) {
            console.error('提交失败', error);
            uni.showToast({ title: '提交失败，请重试：' + error.msg, icon: 'none' });
          }
      },
      getPosition(){
        let resFn;
        let promise = new Promise(res=>resFn = res);
        uni.getLocation({
          type: 'wgs84',
          geocode:true,
          	success: async (res)=> {
              console.log('res',res);
              let Position = await this.$ext.common.getPosition(res);
              console.log('Position',Position);
              resFn(Position)
          	}
        });
        return promise
      },
      async getHospital({pname,cityname}){
        let province = pname,city = cityname;
        let {data:{records:hospitalQuery}} = await this.$api.hospital.hospitalQueryPage({
          current:0,
          size:1000,
          condition:{
            province:province.replace(/省|市/g,''),
            		// 去掉字符串里的市和市辖区还有市市辖区 使用正则祛除
            		city:city.replace(/市市辖区|市辖区|市/g,''),
          }
          })
        hospitalQuery.map(e=>{
          e.text = e.hospitalName;
          e.value = e.id
        })
        this.hospitalQuery = hospitalQuery;
        console.log('hospitalQuery',hospitalQuery);
      },
      async getdeptName(id){
        let {data:getdeptNameMap} = await this.$api.hospital.crawlershospitaldeptQuery({id});
        this.deptNameQuery = [];
        this.deptNameQuery = getdeptNameMap.map(e=>{
          return {...e,text:e.name,value:e.id}
        })
      },
      async getDoctorName({hospitalId,deptId}){
        let {data:getDoctorNameMap} = await this.$api.hospital.crawlershospitaldoctor({hospitalId,deptId});
        console.log('getDoctorNameMap',getDoctorNameMap);
        this.doctorNameQuery = [];
        this.doctorNameQuery = getDoctorNameMap.map(e=>{
          return {...e,text:e.name,value:e.id}
        })
      },
      async getProviderId(city) {
        let {data} = await this.$api.providerManagement.getAccompanyproviderAll({city});

        // 保存原始数据
        this.allProvidersData = data;

        // 转换数据格式
        const formattedData = data.map(e => {
          return {...e, text: e.appName || e.providerName, value: e.id}
        });

        // 获取已选择的服务商ID
        const selectedInitiateId = this.getForm('initiateProviderId').value;
        const selectedProviderId = this.getForm('providerId').value;

        // 过滤掉已经选择的服务商
        this.initiateProviderIdMap = formattedData.filter(item => item.value !== selectedProviderId);
        this.providerIdMap = formattedData.filter(item => item.value !== selectedInitiateId);

        // 如果数据获取成功但为空，显示提示
        if(data && data.length === 0) {
          uni.showToast({
            title: '当前城市没有可用的服务商',
            icon: 'none'
          });
        }
      },

      // 更新服务商列表，去除已选择的项
      updateProviderLists() {
        if (!this.allProvidersData || this.allProvidersData.length === 0) return;

        const formattedData = this.allProvidersData.map(e => {
          return {...e, text: e.appName || e.providerName, value: e.id}
        });

        // 获取已选择的服务商ID
        const selectedInitiateId = this.getForm('initiateProviderId').value;
        const selectedProviderId = this.getForm('providerId').value;

        // 根据已选择的值过滤列表
        this.initiateProviderIdMap = formattedData.filter(item => item.value !== selectedProviderId);
        this.providerIdMap = formattedData.filter(item => item.value !== selectedInitiateId);

        console.log('更新后下单服务商列表:', this.initiateProviderIdMap);
        console.log('更新后接单服务商列表:', this.providerIdMap);
      },
            // 获取服务分类列表
      async getCategoryList(providerId) {
        if (!providerId) return;

        try {
          const res = await this.$api.providerManagement.accompanyserviceclassifyQueryPage({
            current: 1,
            size: 50,
            condition: {
              state: 1,
              providerId: providerId
            },
            // 按orderValue排序
            ascs: 'orderValue'
          });

          if (res.data && res.data.records) {
            this.categoryList = res.data.records;
            return res.data.records;
          }
          return [];
        } catch (error) {
          console.error('获取分类列表失败:', error);
          uni.showToast({
            title: '获取分类失败',
            icon: 'none'
          });
          return [];
        }
      },

      // 显示分类选择
      async showCategorySelect() {
        // 获取服务商ID - 平台端才需要用选择的服务商ID，非平台端使用默认服务商ID
        const providerId = serverOptions.source === 1 ?
          this.getForm('providerId').value : serverOptions.providerId;

        // 获取分类列表
        const categories = await this.getCategoryList(providerId || serverOptions.providerId);

        if (!categories || categories.length === 0) {
          uni.showToast({
            title: '没有可用的服务分类',
            icon: 'none'
          });
          return;
        }

        // 显示分类选择弹窗
        uni.showActionSheet({
          itemList: categories.map(item => item.name),
          success: (res) => {
            const selectedCategory = categories[res.tapIndex];
            // 更新分类选择
            this.setForm('classifyId', 'text', selectedCategory.name);
            this.setForm('classifyId', 'value', selectedCategory.id);
            this.selectedCategoryId = selectedCategory.id;

            // 清空已选服务
            this.setForm('serviceName', 'text', '');
            this.setForm('serviceName', 'value', '');
            this.setForm('price', 'value', '');
            this.setForm('price', 'text', '');
          }
        });
      },
    }
  }
</script>

<style lang="scss">
  .lineHide{
    width: 0;
    overflow: hidden;
    height: 0;
  }
  .pages{
    background: #F4F6FA;
    padding: 0 32rpx;
    box-sizing: border-box;
    height: 100vh;
    width: 100vw;
    overflow: scroll;
  }
  .bottomPlaceholder{
    height: 220rpx;
  }
  .bottomBtn{
    position: fixed;
    bottom: 0;
    left: 0;
    width: 750rpx;
    height: 188rpx;
    background: #FFFFFF;
    box-shadow: 0rpx -1rpx 0rpx 0rpx #EAEBF0;
    padding: 20rpx 30rpx 80rpx;
    box-sizing: border-box;
    display: flex;
    z-index: 9;
    .miniBtnMap{
      display: flex;
      row-gap: 40rpx;
      margin-right: 32rpx;
      .miniBtn{
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        justify-content: center;
        width: 120rpx;
        &:nth-of-type(1){
          margin-right: 40rpx;
        }
        .miniBtnTitle{
          font-weight: 400;
          font-size: 24rpx;
          color: #1D2029;
          width: 100%;
          text-align: center;
          margin-top: 6rpx;
        }
        .miniBtnIcon{
          width: 36rpx;
          height: 36rpx;
        }
      }
    }
    .clockIn{
      flex: 1;
      height: 88rpx;
      background: #00B484;
      border-radius: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #FFFFFF;
      text-align: center;
      line-height: 88rpx;
    }
  }

</style>
