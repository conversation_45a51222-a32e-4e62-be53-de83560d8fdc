<template>
    <view>
      <m-nav-bar :title="doctorTitle" left-icon="left" @clickLeft="back" />
    <page>
        <view slot="content" class="body-main">
            <!-- 添加搜索框 -->
          <view class="search-box">
            <view class="search-input-wrapper">
              <image class="search-icon" :src="searchIcon" mode="aspectFit"></image>
              <input
                class="search-input"
                type="text"
                v-model="searchKeyword"
                placeholder="搜索医生名称"
                confirm-type="search"
                @confirm="handleSearch"
                @input="handleSearch"
              />
              <view class="clear-icon-wrapper" v-if="searchKeyword" @click="clearSearch">
                <image class="clear-icon" :src="clearIcon" mode="aspectFit"></image>
              </view>
            </view>
          </view>
          <!-- 添加筛选栏 -->
          <view class="filter-bar-wrapper">
            <view class="filter-bar">
              <view class="filter-item" @click="$refs.selectCity.show()">
                <text class="filter-text">城市</text>
                <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
              </view>
              <view class="filter-item" @click="selectFilter('hospital')">
                <text class="filter-text">医院</text>
                <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
              </view>
              <view class="filter-item" @click="selectFilter('position')">
                <text class="filter-text">职位</text>
                <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
              </view>
              <view class="filter-item" @click="selectFilter('department')">
                <text class="filter-text">科室</text>
                <image class="filter-arrow" :src="iconRightArrow" mode="aspectFit"></image>
              </view>
            </view>
          </view>
          <view class="m-main-body">
            <scroll-refresh class="scroll-refresh-main" :isShowEmptySwitch="true" :fixed="false" :up="upOption" :down="downOption" @returnFn="returnFn" @scrollInit="scrollInit">
              <view class="doctor-card" v-for="item in indexlist" :key="item.id" @click="handleJump(item.id)">
                <view class="doctor-avatar">
                  <image mode="aspectFill" :src="item.expertPic"></image>
                </view>
                <view class="doctor-info">
                  <view class="doctor-name-title">
                    <text class="doctor-name">{{ item.name }}</text>
                    <text class="doctor-title">{{ item.post }}</text>
                  </view>
                  <view class="doctor-hospital">
                    <image class="hospital-icon" :src="hospitalIcon" mode="aspectFit"></image>
                    <text class="hospital-name">{{ item.hospitalName || '暂无医院信息' }}</text>
                  </view>
                  <view class="doctor-dept">
                    {{ item.deptName || '暂无科室信息' }}
                  </view>
                </view>
              </view>
            </scroll-refresh>
          </view>

          <!-- 添加选择器组件 -->
          <view class="picker-container">
            <selectData ref="hospitalPicker" placeholder="输入医院名称，模糊匹配搜索" :localdata="hospitalOptions" popup-title="请选择医院" @change="onHospitalChange"></selectData>
            <selectData ref="departmentPicker" placeholder="输入科室名称，模糊匹配搜索" :localdata="departmentOptions" popup-title="请选择科室" @change="onDepartmentChange"></selectData>
            <selectData ref="positionPicker" placeholder="输入职位名称，模糊匹配搜索" :localdata="positionOptions" popup-title="请选择职位" @change="onPositionChange"></selectData>
          </view>

          <!-- 城市选择器 -->
          <view class="lineHide">
            <dataPicker ref="selectCity" :localdata="provinceMap" popup-title="请选择城市" @change="onchangeCity" @nodeclick="onnodeclick"></dataPicker>
          </view>
        </view>
    </page>
  </view>
</template>

<script>
  import { isDomainUrl } from '@/utils/index.js'
  import dataPicker from '../components/uni-data-picker/uni-data-picker.vue'
  import selectData from '../components/select-data.vue'
  import serverOptions from '@/config/env/options'

  export default {
    components: {
      dataPicker,
      selectData
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        $constant: this.$constant,
        departmentsList:[],
        doctorList:[],
        currentActive:0,
        depaList:[],
        depaActive:0,
        hospitalId:'',
        deptId:null,
        mescroll: null, // mescroll实例对象
        downOption: {
          auto: false // 不自动加载
        },
        upOption: {
          auto: false, // 不自动加载
          empty: {
            top: 0,
            zIndex: 999,
          },
          onScroll: true
        },
        indexlist:[],
        moreFlag:false,
        icnosType:'bottom',
        curIndex:0,
        tabs: [{ name: '科室医生', consultStatus: '' },{ name: '点评', consultStatus: '' },],
        accompanyMap:[],
        cityName:'',
        doctorTitle: '本地名医', // 动态文案，默认值
        // 添加筛选相关数据
        iconRightArrow: this.$static_ctx + "image/business/hulu-v2/icon-right-arrow.png",
        hospitalIcon: this.$static_ctx + "image/business/hulu-v2/icon-hospital.png", // 医院图标
        searchIcon: this.$static_ctx + "image/business/hulu-v2/icon-search.png",
        clearIcon: this.$static_ctx + "image/business/hulu-v2/icon-clear.png",
        filterParams: {
          city: '',
          hospitalId: '',
          hospitalName: '',
          deptId: '',
          deptName: '',
          position: '',
          doctorName: '' // 添加医生姓名搜索条件
        },
        provinceMap: [], // 省市数据
        hospitalOptions: [], // 医院选项
        departmentOptions: [], // 科室选项
        positionOptions: [ // 职位选项
          { text: '全部', value: '' },
          { text: '主任医师', value: '主任医师' },
          { text: '副主任医师', value: '副主任医师' },
          { text: '主治医师', value: '主治医师' },
          { text: '住院医师', value: '住院医师' }
        ],
        // 添加服务商ID
        providerId: serverOptions.providerId,
        provinceValue: null,
        searchKeyword: '',
        searchTimer: null, // 用于防抖处理
      }
    },
    onLoad(option){
      console.log('option',option);
      if(option.cityName){
        this.cityName = decodeURIComponent(option.cityName)
        this.filterParams.city = this.cityName.split('市')[0] || '';
      }
      if(option.doctorTitle){
        this.doctorTitle = decodeURIComponent(option.doctorTitle)
      }
      // 获取城市列表
      this.getProvinceList();
      this.init()
    },
    methods:{
      back() {
        this.$navto.back(1)
      },
      // 处理搜索逻辑
      handleSearch() {
        // 实现搜索防抖，避免频繁请求
        if (this.searchTimer) {
          clearTimeout(this.searchTimer);
        }

        this.searchTimer = setTimeout(() => {
          console.log('搜索关键词:', this.searchKeyword);
          this.filterParams.doctorName = this.searchKeyword.trim();
          this.init(); // 重新加载数据
        }, 500); // 延迟500ms执行搜索
      },

      // 清除搜索关键词
      clearSearch() {
        this.searchKeyword = '';
        this.filterParams.doctorName = '';
        this.init(); // 重新加载数据
      },

      // 筛选相关方法
      selectFilter(type) {
        switch(type) {
          case 'hospital':
            if (this.hospitalOptions.length === 0) {
              uni.showToast({
                title: '请先选择城市',
                icon: 'none'
              });
              return;
            }
            this.$refs.hospitalPicker.show();
            break;
          case 'department':
            if (!this.filterParams.hospitalId) {
              uni.showToast({
                title: '请先选择医院',
                icon: 'none'
              });
              return;
            }
            this.$refs.departmentPicker.show();
            break;
          case 'position':
            this.$refs.positionPicker.show();
            break;
        }
      },

      // 获取省市列表 - 修改为使用服务商信息
      async getProvinceList() {
        try {
          // 获取服务商信息 - 使用配置中的值
          let providerId = this.providerId;
          if (!providerId) {
            console.error('无效的providerId');
            return;
          }

          console.log('使用的providerId:', providerId);
          let provinceValue = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({
            id: providerId
          })).data;
          this.provinceValue = provinceValue;

          // 获取城市列表
          this.provinceMap = await this.accompanyproviderQueryPage(provinceValue);
        } catch (error) {
          console.error('获取省市列表失败:', error);
          uni.showToast({
            title: '获取省市列表失败',
            icon: 'none'
          });
        }
      },

      // 添加服务商数据处理方法
      async accompanyproviderQueryPage(provinceValue) {
        try {
          // 判断当前是否是平台
          let queryOptions;
          if (serverOptions.source === 1) {
            // 平台获取所有服务商
            const res = await this.$api.accompanyDoctor.getAccompanyproviderAll();
            queryOptions = res.data || [];
            console.log('获取所有服务商数据:', queryOptions);
          } else {
            // 非平台只获取当前服务商
            queryOptions = [provinceValue];
            console.log('使用当前服务商数据:', queryOptions);
          }

          // 检查查询结果是否有效
          if (!queryOptions || queryOptions.length === 0) {
            console.error('无有效的服务商数据');
            return [];
          }

          // 生成城市映射
          let cityMap = this.getCityMap(queryOptions);
          return [...new Set(cityMap)].filter(e=>e);
        } catch (error) {
          console.error('处理服务商数据失败:', error);
          return [];
        }
      },

      // 城市数据处理
      getCityMap(AccompanyproviderAll) {
        try {
          return AccompanyproviderAll.reduce((acc, current) => {
            // 确保数据有效
            if (!current || !current.province || !current.city) {
              console.warn('跳过无效的省市数据:', current);
              return acc;
            }

            let provinceMap = current.province.split(',');
            let cityMap = current.city.split('$');

            provinceMap.forEach((provinceItem, index) => {
              // 检查是否有对应的城市数据
              if (!cityMap[index]) {
                console.warn(`省份 ${provinceItem} 没有对应的城市数据`);
                return;
              }

              let currentCityMap = cityMap[index].split(',').filter(e=>e);
              let prov = acc.find(p => p.value === provinceItem);
              if (!prov) {
                acc.push(prov = {text: provinceItem, value: provinceItem, children: []});
              }
              prov.children.push(...currentCityMap);
            });

            return acc;
          }, []).map(e => {
            e.children = [...new Set(e.children)];
            e.children = e.children.map(c => ({text: c, value: c}));
            return e;
          });
        } catch (error) {
          console.error('处理城市数据映射失败:', error);
          return [];
        }
      },

      // 城市选择变化处理
      async onchangeCity({detail:{value}}) {
        if (!value || value.length < 2) return;

        let [province, city] = value.map(e => e.value);
        this.filterParams.city = city.split('市')[0] || city;

        // 更新UI显示
        this.cityName = this.filterParams.city;

        // 清空其他筛选条件
        this.filterParams.hospitalId = '';
        this.filterParams.hospitalName = '';
        this.filterParams.deptId = '';
        this.filterParams.deptName = '';

        // 获取医院列表
        await this.getHospitalList();

        // 重新加载数据
        this.init();
      },

      // 获取医院列表
      async getHospitalList() {
        try {
          let {data:{records:hospitalList}} = await this.$api.hospital.hospitalQueryPage({
            current: 0,
            size: 1000,
            condition: {
              city: this.filterParams.city,
            }
          });

          this.hospitalOptions = hospitalList.map(e => ({
            text: e.hospitalName,
            value: e.id
          }));
        } catch (error) {
          console.error('获取医院列表失败:', error);
          this.hospitalOptions = [];
        }
      },

      // 医院选择回调
      async onHospitalChange({detail:{value}}) {
        if (!value || !value[0]) return;

        this.filterParams.hospitalId = value[0].value;
        this.filterParams.hospitalName = value[0].text;

        // 清空科室
        this.filterParams.deptId = '';
        this.filterParams.deptName = '';

        // 获取科室列表
        await this.getDepartmentList();

        // 重新加载数据
        this.init();
      },

      // 获取科室列表
      async getDepartmentList() {
        try {
          const {data} = await this.$api.hospital.crawlershospitaldeptQuery({
            id: this.filterParams.hospitalId
          });

          this.departmentOptions = data.map(e => ({
            text: e.name,
            value: e.id
          }));
        } catch (error) {
          console.error('获取科室列表失败:', error);
          this.departmentOptions = [];
        }
      },

      // 科室选择回调
      onDepartmentChange({detail:{value}}) {
        if (!value || !value[0]) return;

        this.filterParams.deptId = value[0].value;
        this.filterParams.deptName = value[0].text;

        // 重新加载数据
        this.init();
      },

      // 职位选择回调
      onPositionChange({detail:{value}}) {
        if (!value || !value[0]) return;

        this.filterParams.position = value[0].value;

        // 重新加载数据
        this.init();
      },

      onnodeclick() {},

      // 原有方法
      returnFn(obj) {
        console.log('this.mescroll3',this.mescroll);
        const that = this
        setTimeout(()=> {
          // 构建查询条件
          const condition = {
            city: this.filterParams.city || this.cityName?.split('市')[0] || '',
          };

          // 添加其他筛选条件
          if (this.filterParams.hospitalId) {
            condition.hospitalId = this.filterParams.hospitalId;
          }

          if (this.filterParams.deptId) {
            condition.deptId = this.filterParams.deptId;
          }

          if (this.filterParams.position) {
            condition.post = this.filterParams.position;
          }

          // 添加医生姓名搜索条件
          if (this.filterParams.doctorName) {
            condition.name = this.filterParams.doctorName;
          }

          let params = {
            current: obj.pageNum,
            size: obj.pageSize,
            condition: condition
          }

          console.log('筛选条件:', params);

          let defaultUrl = '0/msg-reply/1013082175633223682.png';
          that.$api.hospital.crawlershospitaldoctorQueryPage(params).then(res => {
              // 使用原来的处理方式，同时添加默认值处理
              let data = res.data.records.map(item=>({
                ...item,
                expertPic: isDomainUrl(item.expertPic || defaultUrl),
                hospitalName: item.hospitalName || '暂无医院信息',
                deptName: item.deptName || '暂无科室信息',
                post: item.post || ''
              })) || []
              if (obj.pageNum === 1) {
                  that.indexlist = []
              }
              that.indexlist = [...that.indexlist, ...data]
              obj.successCallback && obj.successCallback(data)
          })
        }, that.$constant.noun.scrollRefreshTime)
      },

      scrollInit(scroll) {
        scroll.optUp.page.num = 1
        scroll.optUp.page.size = 10
        this.mescroll = scroll
        console.log('this.mescroll1',this.mescroll);
      },

      init() {
        this.$nextTick(() => {
          console.log('this.mescroll2',this.mescroll);
          this.mescroll && this.mescroll.triggerDownScroll()
        })
      },
      handleJump(id){
        this.$navto.push('DoctorDetail', {id, employeeTitle: this.employeeTitle})
      },
    },
 }
</script>

<style lang='scss' scoped>
/* 添加筛选栏样式 */
.filter-bar-wrapper {
  position: relative;
  z-index: 10;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #F2F2F2;
  width: 100%;
}

.filter-bar {
  display: flex;
  width: 100%;
  height: 88rpx;
}

.filter-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.filter-text {
  font-size: 28rpx;
  color: #333333;
  margin-right: 8rpx;
  font-weight: 500;
}

.filter-arrow {
  width: 24rpx;
  height: 24rpx;
  transform: rotate(90deg);
  position: relative;
  top: 2rpx;
}

/* 添加搜索框样式 */
.search-box {
  position: relative;
  z-index: 10;
  background-color: #FFFFFF;
  border-bottom: 1rpx solid #F2F2F2;
  width: 100%;
  padding: 20rpx 30rpx;
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background-color: #F5F5F5;
  border-radius: 36rpx;
  padding: 16rpx 24rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.search-input {
  flex: 1;
  font-size: 28rpx;
  color: #333333;
  height: 36rpx;
  line-height: 36rpx;
}

.clear-icon-wrapper {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.clear-icon {
  width: 24rpx;
  height: 24rpx;
}

/* 弹窗选择器样式 */
.picker-container {
  position: absolute;
  width: 0;
  height: 0;
  overflow: hidden;
}

/* 隐藏容器样式 */
.lineHide {
  width: 0;
  overflow: hidden;
  height: 0;
}

/* 原有样式 */
.line{
    display: block;
    width: 100%;
    height: 1upx;
    background-color: #e0e0e0;
    transform: scaleY(.333);
}
.body-main{
    height: 100%;
    background-color: #f5f5f5;
    .m-main-body{
      height: calc(100% - 88rpx - 92rpx); /* 减去筛选栏和搜索框的高度 */
      .scroll-refresh-main{
        height: 100%;
        ::v-deep .mescroll-uni{
          .z-paging-content{
            // background-color: #fff !important;
            // background-color: pink !important;
          }
        }

        /* 医生卡片样式更新 */
        .doctor-card{
          background-color: #fff;
          margin: 20rpx 24rpx 0;
          padding: 30rpx 24rpx;
          display: flex;
          border-radius: 12rpx;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

          .doctor-avatar{
            width: 120rpx;
            border-radius: 8rpx;
            overflow: hidden;

            image{
              width: 100%;
              height: 100%;
              object-fit: cover;
            }
          }

          .doctor-info{
            flex: 1;
            margin-left: 20rpx;
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .doctor-name-title{
              display: flex;
              align-items: center;

              .doctor-name{
                color: #333333;
                font-size: 34rpx;
                font-weight: 500;
              }

              .doctor-title{
                margin-left: 12rpx;
                color: #666666;
                font-size: 28rpx;
              }
            }

            .doctor-hospital{
              display: flex;
              align-items: center;
              margin-top: 12rpx;

              .hospital-icon{
                width: 28rpx;
                height: 28rpx;
                margin-right: 8rpx;
              }

              .hospital-name{
                color: #666666;
                font-size: 26rpx;
                flex: 1;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }

            .doctor-dept{
              margin-top: 10rpx;
              color: #00A277;
              font-size: 26rpx;
              background-color: #EBF7F5;
              display: inline-block;
              padding: 4rpx 12rpx;
              border-radius: 4rpx;
              max-width: fit-content;
            }
          }
        }
      }
    }
}
</style>
