<template>
    <view
      class="content-box"
      @touchstart="touchstart"
      id="content-box"
      :style="bottomBoxHeight ? `padding-bottom: ${bottomBoxHeight}px` : ''"
    >
        <!-- 背景图- 定位方式 -->
        <view class="ws-warn" v-if="webSocketWarningText.length > 0">
            {{ webSocketWarningText }}
        </view>
        <view class="content-box-loading" v-if="messageListLoadingStatus !== 3">
            <view class="icon-loading"></view>
        </view>
        <view class="message" v-for="(item,index) in messageList" :key="item.hasBeenSentId"
                :id="`msg-${item.hasBeenSentId}`">
            <view v-if="isShowTime(index,messageList) && ![6,8].includes(item.touchType)" class="time"><text class="time-text">{{item.updateTime | getTimeStringAutoShort2}}</text></view>
            <view
              class="message-item "
              :class="item.isItMe ? 'right' : 'left'"
              v-if="![6,8].includes(item.touchType)"
            >
                <image class="img avatar" v-if="item.isItMe" :src="chatItem.userHeadPath ? file_ctx + chatItem.userHeadPath : defaultAvatar"></image>
                <image class="img avatar" v-else :src="chatItem.chatUserHeadPath ? file_ctx + chatItem.chatUserHeadPath : defaultAvatar"></image>
                <!-- msgType = 1 文本 -->
                <view class="content" v-if="item.msgType == 1">{{ item.content }}</view>
                <!-- msgType = 2 图片 -->
                <view
                    class="content msgType2"
                    v-else-if="item.msgType == 2"
                    @tap="viewImgs(item,index)"
                >
                    <image :src="file_ctx + item.content" class="img" mode="widthFix"></image>
                </view>

                <!-- 视频 -->
                <view class="content msgType2" v-else-if="item.msgType === 3">
                    <video style="height: 200px;" :src="file_ctx + item.content" controls class="img" />
                </view>
                <!-- 文件 -->
                <view class="content" v-else-if="item.msgType === 4">
                    <view
                        style="font-size: 28rpx;"
                        @tap="uploadFile(file_ctx + item.content)"
                    >
                        <template v-if="item.desc">
                            {{ JSON.parse(item.desc)[0].name }}
                        </template>
                    </view>
                </view>
                <!-- H5卡片 -->
                <view v-else-if="item.msgType === 5" class="content card" @tap="clickCard(item)">
                    <template v-if="item.msgContent">
                        <view class="card-content">
                            <view class="card-content-title">{{ item.text }}</view>
                            <view class="card-content-desc">{{ item.desc }}</view>
                        </view>
                        <image :src="file_ctx + item.attach" alt="" class="card-img">
                    </template>
                </view>
                <!-- 小程序卡片 -->
                <view v-else-if="item.msgType === 6" class="content card" @tap="clickCard(item)">
                    <template v-if="item.msgContent">
                        <view class="card-content">
                            <view class="card-content-title">{{ item.text }}</view>
                            <view class="card-content-desc">{{ item.desc }}</view>
                        </view>
                        <image :src="file_ctx + item.attach" alt="" class="card-img">
                    </template>
                </view>

                <!-- msgType = 8 业务卡片 -->
                <view
                  v-else-if="item.msgType == 8"
                  class="content bgf"
                >
                  <view class="content-title">
                  <image  :src='$static_ctx + "image/business/im/icon-im-zxl.png"' mode="" class="zxlicon"></image>
                  {{ item.text }}
                  </view>
                  <view class="content-card">
                    <template v-if="item.desc">
                      <view class="content-card-item" v-if="item.desc.patientInfoVo">
                        <view class="content-card-key">咨询人：</view>
                        <view class="content-card-value">
                          {{ `${item.desc.patientInfoVo.name} | ${item.desc.patientInfoVo.gender === 1 ? '男' : item.desc.patientInfoVo.gender === 2 ? '女' : '未知' } | ${item.desc.patientInfoVo.age||''}`}}
                        </view>
                      </view>
                      <view class="content-card-item" v-if="item.desc.gfConsultTypeName">
                        <view class="content-card-key">咨询类型：</view>
                        <view class="content-card-value">{{ item.desc.gfConsultTypeName }}</view>
                      </view>
                      <view class="content-card-item" v-if="item.desc.gfDepartmentName">
                        <view class="content-card-key">咨询问题：</view>
                        <view class="content-card-value">{{ item.desc.gfDepartmentName }}</view>
                      </view>
                      <view class="content-card-item" v-if="item.desc.gfIssue">
                        <view class="content-card-key">咨询内容：</view>
                        <view class="content-card-value">{{ item.desc.gfIssue }}</view>
                      </view>
                      <view class="content-card-item" v-if="item.desc.gfAttachs">
                        <view class="content-card-key" style="min-width: 140upx;width: 140upx;">图片：</view>
                        <image @tap="viewImg([file_ctx + item.desc.gfAttachs])" class="content-card-value" mode="aspectFit" :src="file_ctx + item.desc.gfAttachs"></image>
                      </view>
                    </template>
                  </view>
                </view>
                <view class="icon-loading msg-loading" v-if="item.msgCloudStatus === 2"></view>
                <!-- <view class="read-status-panel" :class="item.readStatus==1 ? 'icon-yijianfankui-d-ok' : 'icon-yijianfankui-d'"></view> -->
            </view>

            <!-- 3 科室引导语  4 咨询问题引导语 5 选择咨询人档案引导语 -->
            <view
              v-if="orderDetail.consultStatus !== 3
              && item.isItMe
              && item.orderId === orderDetail.id
              && [3,4,5,7].includes(item.touchType)"
              @click="editOrder(item)"
              class="edit"
            >修改</view>

            <!-- 订阅消息 -->
            <view class="subscribe" v-if="item.touchType === 6">
                <view class="subscribe-title">申请授权</view>
                <view class="subscribe-desc">
                    提醒您：为了让您更及时的接收消息，请您开通一下通知服务：
                </view>
                <view style="margin: 24upx 0;">
                  <view v-if="env.isOpenMsgSubscribe" class="subscribe-steps" :class="subscribeStep === 1 ? 'active' : ''">
                    <text class="subscribe-steps-num">1</text>
                    <text class="subscribe-steps-desc">接收小程序订阅消息</text>
                  </view>
                  <view
                    v-if="env.isOpenAccountSubscribe"
                    class="subscribe-steps"
                    :class="(
                      (!env.isOpenMsgSubscribe && subscribeStep === 1) ||
                      (env.isOpenMsgSubscribe && subscribeStep === 2)
                    ) ? 'active' : ''">
                    <text class="subscribe-steps-num">{{ env.isOpenMsgSubscribe ? 2 : 1 }}</text>
                    <text class="subscribe-steps-desc">接收公众号订阅消息</text>
                  </view>
                </view>
                <button
                  class="btn"
                  type="primary"
                  size="mini"
                  :disabled="subscribeStep !== 1 || !openid"
                  @click="subscribeMsg"
                >{{ subscribeStep === 1 ? '立即开通' : '已开通' }}</button>
            </view>

            <!-- 服务评价 -->
            <view v-if="item.touchType === 8 && orderDetail.id === item.orderId">
              <view class="order-end-evaluate">
                <view class="order-end-evaluate-title">请您对本次服务进行评价</view>
                <view class="order-end-evaluate-desc">真实有效的评价是我们提升服务的最大动力</view>
                <view style="width: 100%; margin-top: 32upx;">
                  <evaluate
                    :form="orderEvaluate"
                    :disabled="orderDisable"
                    @confirm="confirmEvaluate($event, item)"
                    @handle='handleConsult'
                  />
                </view>
              </view>
            </view>
        </view>

        <view class="order-end" v-if="orderDetail.consultStatus === 3">
          <view class="order-end-time">{{ orderDetail.endTime | getTimeStringAutoShort2 }}</view>
          <view style="text-align: center;"><view class="order-end-tips">服务已结束</view></view>
          <text>您的咨询服务已完成，如需继续咨询，可点击下方按钮继续咨询</text>
          <br/>
          <button class="btn zxbtn" type="primary" size="mini" @tap="handleConsult">继续咨询</button>
        </view>

        <!-- 超时提醒 -->
        <view class="subscribe" style="box-sizing: border-box; margin: 24rpx 50rpx;" v-if="orderDetail.consultStatus !== 3 && timeoutReminderShow">
            <view class="subscribe-close-icon" @tap="closeTimeoutReminder">x</view>
            <view class="subscribe-desc">
                {{ `${timeoutReminderMinuteFormat}` }}
            </view>
            <button class="btn" type="primary" size="mini" @tap="endOrder">已解决啦！结束咨询</button>
        </view>

        <!-- <view :class="{'unread-fixed-box-top': topUnRead, 'unread-fixed-box': !topUnRead}" v-if="showUnReadMsgHint" @click="checkUnRead">
          <text class="unread-fixed-text">{{ topUnRead ? '您有历史消息未读' : '您有最新未读消息' }}</text>
        </view> -->

		<!-- 结束咨询弹框 -->
		<showmodel type='center' dialogStyle='width:632rpx;height:289rpx;border-radius:10rpx' :show='centerVisible' @cancel='cancelCenterVisible' root-class='centerRoot'>

			<view class="dialogContent">
				<view class="dialog-title">
					{{centerMsg}}
				</view>
				<view class="dialog-btns">
					<view class="dialog-btn" @click='submitConsult'>
						{{centertype == 1 ? '确定' : '结束咨询'}}
					</view>
					<view class="dialog-space">

					</view>
					<view class="dialog-btn active" @click='cancelCenterVisible'>
						再想想
					</view>
				</view>
			</view>

		</showmodel>
    </view>
</template>

<script>
import { mapState } from 'vuex'
import TimeUtils from '@/common/util/websocket/timeUtils.js'
import evaluate from './evaluate.vue'
// import HandleSingleChat from '@/service/ext/modules/websocket/receive/HandleSingleChat'
import env from '@/config/env'

import showmodel from '@/components/basics/showmodel/showmodel.vue'
import { timeDiff } from '../../../../utils'

export default {
  components: {
    evaluate,
	showmodel
  },
  data() {
    return {
      env,
      centerVisible:false,
      centerMsg:"是否确认继续咨询？",
      centertype:1,

      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      regForm: {
        platformNum: 0,
        campusNum: 0
      },
      formData: {
        content: '',
        limit: 20,
        index: 1
      },
      loading: true, //标识是否正在获取数据
      imgHeight: '1000px',
      mpInputMargin: false, //适配微信小程序 底部输入框高度被顶起的问题
      chatType: "voice",  // 图标类型 'voice'语音 'keyboard'键盘
      PointY: 0, //坐标位置
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
      billingKey: [],
      showUnReadMsgHint: false,
      topUnRead: false,
      readEl: null,
      contentBoxElInfo: {},
      consultingconfig: {}, // 咨询系统配置
      consultingconfigTimer: null, // 监测超时提醒定时器
      timeoutReminderShow: false, // 超时弹窗提醒
      timeoutReminderMinute: 0, // 超时时间戳
      subscribeStep: 1,
      openid: ''
    }
  },
  created () {
    uni.getSysteminfo({
      success: res => {
        console.log('res-------------------------', res)
      }
    })
  },
  async mounted() {
	  // setTimeout(() => {
		 //  this.centerVisible = true
	  // }, 800);
    console.log('init--------------')
    this.$common.setKeyVal('chat', 'messageInit', false, false)
    this.getConsultingconfigQueryconfig().then(() => {
      this.initConsultingconfigTimer()
      setTimeout(() => {
        this.initOrderStatus()
      }, 5000)
    })
    this.joinData()
    if (env.isOpenAccountSubscribe) {
      this.checkAccountSubscribeStatus().then((res) => {
        if (!res) return
        this.subscribeStep = 2
      })
    }
    this.openid = await this.$ext.wechat.getOpenId()
  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
    }),
    ...mapState('chat', {
      ws: state => state.ws,
      webSocketIsOpen: state => state.webSocketIsOpen,
      webSocketWarningText: state => state.webSocketWarningText,
      chatItem: state => state.chatItem,
      messageList: state => state.messageList,
      showFunBtn: state => state.showFunBtn,
      bottomBoxHeight: state => state.bottomBoxHeight,
      messageListLoadingStatus: state => state.messageListLoadingStatus,
      orderDetail: state => state.orderDetail,
      nodereplyconfig: state => state.nodereplyconfig,
    }),
    // 小程序订阅消息节点配置
    nodeConfig() {
        return this.nodereplyconfig.find(item => item.pushType === 6)
    },
    // 订单评价
    orderEvaluate () {
      const { seReplyQuality = '', seServiceAttitude = '', seReplySpeed = '', seEvaluate = '' } = this.orderDetail || {}
      return [
        { key: 'seReplyQuality', value: seReplyQuality, label: '回复质量', type: 'rate' },
        { key: 'seServiceAttitude', value: seServiceAttitude, label: '服务态度', type: 'rate' },
        { key: 'seReplySpeed', value: seReplySpeed, label: '回复速度', type: 'rate' },
        { key: 'seEvaluate', value: seEvaluate, label: '评价文本', placeholder: '🖊谈谈对您本次就诊的印象吧~',placeholder: '谈谈对您本次就诊的印象吧~', type: 'text', config: {label: '',style:{width: '100%'},inputStyle:'padding-left:35px;font-size:14px;background-color:#fff'} },
      ]
    },
    // 订单是否已结束
    orderDisable () {
      if (this.$validate.isNull(this.orderDetail) || this.orderDetail.seReplyQuality || this.orderDetail.seServiceAttitude || this.orderDetail.seReplySpeed || this.orderDetail.seEvaluate) {
        return true
      } else {
        return false
      }
    },
    // 将超出分钟格式化好看点
    timeoutReminderMinuteFormat() {
      if (!this.timeoutReminderMinute) return ''
      const timestamp = this.timeoutReminderMinute*60*1000 // 原单位分钟 转成时间戳
      const hourTimestamp = 1000*60*60 // 一小时时间戳
      const dayTimestamp = hourTimestamp*24 // 一天时间戳
      const weekTimestamp = dayTimestamp*7 // 七天时间戳
      let day = parseInt(this.$accurateConversion.divide(timestamp, dayTimestamp))
      let hour = parseInt(this.$accurateConversion.divide(timestamp%dayTimestamp, hourTimestamp))
      let minute = parseInt(this.$accurateConversion.divide(timestamp%hourTimestamp, 60*1000))
      if (day > 7) {
        return `已经很久没有收到你的回复啦，是否已解决了你的问题？`
      }
      day = day ? day + '天' : ''
      hour = hour ? hour + '小时' : ''
      minute = minute ? minute + '分钟' : ''
      return `已经${day}${hour}${minute}没收到你的回复啦，是否已解决了你的问题`
    }
  },
  beforeDestroy() {
    if (this.consultingconfigTimer) {
      clearInterval(this.consultingconfigTimer)
      this.consultingconfigTimer = null
    }
  },
  watch: {
    orderDetail: {
      handler () {
        if (!env.isOpenMsgSubscribe) return
        if (!this.$validate.isNull(this.orderDetail) && this.orderDetail.subscribeStatus === 1) {
          this.subscribeStep = 2
        } else {
          this.subscribeStep = 1
        }
      },
      deep: true
    },
    messageList: {
      handler (newData, oldData) {
		    // 更新是否超市提醒
        if (this.$validate.isNull(oldData)) {
          this.timeoutReminder()
        }
        this.$common.setKeyVal('chat', 'messageListEl', this, false)
        // 是否有未读消息不在可视区域内
        const unReadList = this.messageList.filter(item => {
          return item.readStatus === 2 && item.fromUserId + '' == this.chatItem.chatUserId + ''
        })
        this.$nextTick(async () => {
          for (let i = 0; i < unReadList.length; i++) {
            let item = unReadList[i]
            const readEl = '#msg-' + item.hasBeenSentId
            // #ifdef H5
            const el = document.getElementById('msg-' + item.hasBeenSentId)
            // #endif

            // #ifndef H5
            const query = uni.createSelectorQuery().in(this)
            const el = query.select(readEl)
            // #endif

            const {isShow, isTop} = await this.isElementInViewportPos(el)

            if (!isShow) {
              this.topUnRead = isTop
              this.showUnReadMsgHint = true
              this.readEl = readEl
              return
            }
          }
          this.showUnReadMsgHint = false
        })

      },
      deep: true
    },
    showFunBtn () {
      this.$nextTick(() => {
        this.scrollBottom()
      })
    },
  },
  methods: {
    /**
     * 初始化订单状态
     */
    initOrderStatus() {
      // 超时没回复自动结束订单再重新生成订单
      const { overtimeSwitch, overtimeMinute } = this.consultingconfig
      console.log('overtimeSwitch----', overtimeSwitch, this.timeoutReminderMinute, overtimeMinute)
      if (overtimeSwitch !== 1 || this.timeoutReminderMinute <= overtimeMinute) return

      // 关闭订单
      const data =  {
        cmd: this.$constant.chat.ORDER_END_CMD,
        data: {
          orderId: this.chatItem.orderId,
          userId: this.chatItem.userId,
          endType: 1,
        }
      }
      this.$ext.webSocket.webSocketSend(this.$constant.chat.ORDER_END_CMD, data)
    },
    initConsultingconfigTimer() {
      const that = this
      if (that.consultingconfigTimer) {
        clearInterval(that.consultingconfigTimer)
        that.consultingconfigTimer = null
      }
      const { timeoutReminderSwitch } = that.consultingconfig
      function cb () {
        const lastMsg = !that.$validate.isNull(that.messageList) ? that.messageList[that.messageList.length - 1] : {}
          let timestamp = lastMsg.updateTime
          try {
              timestamp = Number(timestamp) ? Number(timestamp) : timestamp
          } catch (err) {}
          const timeoutReminderTime = new Date().getTime() - new Date(timestamp).getTime()
          that.timeoutReminderMinute = (timeoutReminderTime/60/1000).toFixed(0)
          if (!that.timeoutReminderShow) {
            that.timeoutReminder()
          }
      }
      cb()
      // 开启定时器
      if (timeoutReminderSwitch === 1) {
        that.consultingconfigTimer = setInterval(() => {
          cb()
        }, 1000)
      }
    },
	  cancelCenterVisible(){
		  this.centerVisible = false
	  },
    // toJSON () {},
    closeTimeoutReminder () {
      this.timeoutReminderShow = false
      if (this.consultingconfigTimer) {
        clearInterval(this.consultingconfigTimer)
        this.consultingconfigTimer = null
      }
    },
    endOrder () {
	  this.centerMsg = '确认结束咨询？';

	  // console.log(this.centerMsg)
	  this.centerVisible = true;
	  this.centertype = 2;


      // this.$uniPlugin.modal('','确认结束咨询？', {
      //   showCancel: true, // 是否显示取消按钮，默认为 true
      //   cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
      //   cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
      //   confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
      //   confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
      //   fn: async (n) => {
      //     if(n) {
      //       const data =  {
      //           cmd: this.$constant.chat.ORDER_END_CMD,
      //           data: {
      //               orderId: this.chatItem.orderId,
      //               userId: this.chatItem.userId,
      //               endType: 1,
      //           }
      //       }
      //       this.$ext.webSocket.webSocketSend(this.$constant.chat.ORDER_END_CMD, data)
      //     }
      //   }
      // })
    },
    // 获取咨询系统配置
    async getConsultingconfigQueryconfig () {
      const that = this
      const res = await that.$api.chat.consultingconfigQueryconfig({})
      that.consultingconfig = res.data
    },
    // 是否超出需提醒时间
    async timeoutReminder () {
      const that = this
      const { timeoutReminderSwitch	, timeoutReminderMinute = 0 } = that.consultingconfig || {}
      if (this.$validate.isNull(that.consultingconfig) || timeoutReminderSwitch !== 1) return
      const lastMsg = !that.$validate.isNull(that.messageList) ? that.messageList[that.messageList.length - 1] : {}
      if (that.$validate.isNull(lastMsg)) return
      let timestamp = lastMsg.updateTime
      try {
          timestamp = Number(timestamp) ? Number(timestamp) : timestamp
      } catch (err) {

      }
      const timeoutReminderTime = new Date().getTime() - new Date(timestamp).getTime()
      // 超出提醒
      if (timeoutReminderTime >= (timeoutReminderMinute * 60 * 1000)) {
        that.timeoutReminderMinute = (timeoutReminderTime/60/1000).toFixed(0)
        that.timeoutReminderShow = true
        let messageList = that.messageList
        const item = messageList[messageList.length - 1]
        if (!item) {
            that.$nextTick(() => {
                uni.pageScrollTo({
                    scrollTop: 99999,
                    duration: 0
                })
            })
            return
        }
        // #ifdef H5
        var getDiv = document.getElementById('msg-' + item.hasBeenSentId)
        // #endif
        // #ifndef H5
        const query = uni.createSelectorQuery().in(that)
        var getDiv = query.select('#msg-' + item.hasBeenSentId)
        // #endif
        if (getDiv!=null){
          if (getDiv.id !== undefined || getDiv.boundingClientRect !== undefined) {
            let obj = {id: item.hasBeenSentId, readStatus: item.readStatus}
            // 曝光上报
            if (await that.isElementInViewport(getDiv) && !that.isDivDisplay(getDiv)) {
              that.$nextTick(() => {
                  uni.pageScrollTo({
                      scrollTop: 99999,
                      duration: 0
                  })
              })
            }
          }
        }

        // clearInterval(that.consultingconfigTimer)
        // that.consultingconfigTimer = null
      } else {
        that.timeoutReminderShow = false
      }
    },
    clickCard (e) {
      const { text, urlPath, minAppid } = e
      switch (e.msgType) {
        // H5卡片
        case 5:
          this.$navto.push('WebHtmlView', { src: urlPath, title: text })
          break
        // 小程序卡片
        case 6:
          console.log(minAppid, e)
          uni.navigateToMiniProgram({
            appId: minAppid,
            path: urlPath, // 打开的页面路径
            extraData: {}, // 需要传递的参数
            success: res => {

            },
            fail: err => {
              console.log('打开失败------', err)
            }

          })
          break
        default:
      }
    },
    uploadFile () {

    },
	submitConsult(){
		var that = this;
		if(this.centertype == 1){
				const tenantId = that.$common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId
				that.$uniPlugin.loading('加载中', true)
				that.$api.order.orderEndInitiatorUserCheck({ userId: this.codeUserInfo.id, tenantId, chatUserId: that.chatItem.chatUserId }).then(res => {
				  that.$uniPlugin.hideLoading()
				  that.$navto.replace('Chat', res.data)
				  that.centerVisible = false;
				}).catch(() => {
				  that.$uniPlugin.hideLoading()
				  that.centerVisible = false;
				})
		}else if(this.centertype == 2){
              const data =  {
                  cmd: this.$constant.chat.ORDER_END_CMD,
                  data: {
                      orderId: this.chatItem.orderId,
                      userId: this.chatItem.userId,
                      endType: 1,
                  }
              }
              this.$ext.webSocket.webSocketSend(this.$constant.chat.ORDER_END_CMD, data)
			  that.centerVisible = false;
		}
	},
    // 继续咨询
    handleConsult () {
      const that = this;

	  this.centerMsg = '是否确认继续咨询？';

	  console.log(this.centerMsg)
	  this.centerVisible = true;
	  this.centertype = 1;

      // const modalConfig = {
      //   showCancel: true, // 是否显示取消按钮，默认为 true
      //   cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
      //   cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
      //   confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
      //   confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
      //   fn: async (n) => {
      //     if (n) {
      //       const tenantId = that.$common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId
      //       that.$uniPlugin.loading('加载中', true)
      //       that.$api.order.orderEndInitiatorUserCheck({ userId: this.codeUserInfo.id, tenantId, chatUserId: that.chatItem.chatUserId }).then(res => {
      //         that.$uniPlugin.hideLoading()
      //         that.$navto.replace('Chat', res.data)
      //       }).catch(() => {
      //         that.$uniPlugin.hideLoading()
      //       })
      //     }
      //   }
      // }
      // that.$uniPlugin.modal('','是否确认继续咨询？',modalConfig)
    },
    // 查看图片（多张）
    viewImgs (e, eIndex) {
      let current = 0
      const imgs = this.messageList.filter(item => {
        return item.msgType == 2
      }).map((item, index) => {
        if (item.id === e.id) {
          current = index
        }
        return this.file_ctx + item.content
      })
      uni.previewImage({
        current,
        urls: imgs,
        // #ifndef MP-WEIXIN
        indicator: 'number'
        // #endif
      })
    },
    /**
     * 检查用户公众号订阅状态
     * @return Promise<boolean> 是否已经订阅
     */
    async checkAccountSubscribeStatus() {
      const unionid = await this.$ext.wechat.getUnionId()
      const res = await this.$api.common.accountattentionSubscribeOrNot({
        unionid,
        wxId: 'wx0918ff821e41f5c4'
      })
      if (!this.$validate.isNull(res.data)) return Promise.resolve(true)
      return Promise.resolve(false)
    },
    /**
     * 公众号订阅
     */
    accountSubscribe() {
      return new Promise(async (resolve, reject) => {
        this.$uniPlugin.modal('您还没关注消息助手，请前往开启', '', {
          showCancel: true, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if (n) {
              this.subscribeStep = 3
              this.navtoGo('WebHtmlView', { src: env.subscribeUrl, title: '消息订阅' })
              resolve()
            } else {
              reject()
            }
          }
        })
      })
    },
    /**
     * 消息订阅
     */
    msgSubscribe() {
      const that = this
      return new Promise(async (resolve, reject) => {
        await this.$uniPlugin.subscribeMessage().catch(() => { resolve() })
        uni.requestSubscribeMessage({
          tmplIds: ['s26obqeypTtrQZHJVoe8DU7EiD_IPYGAUC2-zD6GzzM'],
          success: (res) => {
            const nodeConfigReplyContent = {
              subscribeStatus: 1, // 用户订阅状态 1-是 2-否
              initiatorUserAppOpenId: this.openid,
            }
            // 更新订单详情
            const data =  {
                cmd: that.$constant.chat.CONSULT_CMD,
                data: {
                    orderId: that.chatItem.orderId,
                    userId: that.chatItem.userId,
                    subscribeStatus: 1, // 用户订阅状态 1-是 2-否
                    initiatorUserAppOpenId: this.openid,
                    nodeConfigId: this.nodeConfig.id,
                    nodeConfigReplyContent: JSON.stringify(nodeConfigReplyContent)
                }
            }
            that.$ext.webSocket.webSocketSend(that.$constant.chat.CONSULT_CMD, data)
            that.subscribeStep = 2
            resolve()
          },
          fail: (err) => {
            resolve()
            console.log('调用失败-----------------', err)
          }
        })
      })
    },
    async subscribeMsg () {
      if (env.isOpenMsgSubscribe) {
        await this.msgSubscribe()
        }
      if (env.isOpenAccountSubscribe) {
        await this.accountSubscribe()
      }
    },
    // 查看未读消息
    checkUnRead () {
      console.log('this.readEl-------------------------', this.readEl)
      const query = uni.createSelectorQuery().in(this);
      query.select('#content-box').boundingClientRect(one => {
        this.contentBoxElInfo = one
        this.bindScroll(this.readEl)
      }).exec()
    },
    confirmEvaluate (list, messageItem) {
      let that = this
      const { ORDER_EVALUATE_CMD, ORDER_GUIDE } = that.$constant.chat
      let param = {}
      list.forEach(item => {
        param[item.key] = item.value
      })
      // 用户评价节点配置
      // const nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig').find(item => {
      //   return item.pushType === 8
      // })
      // let chatDto =  {
      //     cmd: ORDER_GUIDE,
      //     data: {
      //         orderId: messageItem.orderId,
      //         userId: this.chatItem.userId,
      //         chatUserId: this.chatItem.chatUserId,
      //         nodeConfigId: nodereplyconfig.id
      //     }
      // }
      // that.$ext.webSocket.webSocketSend(ORDER_GUIDE, chatDto)

      const data =  {
        cmd: ORDER_EVALUATE_CMD,
        data: {
          userId: this.chatItem.userId,
          orderId: messageItem.orderId,
          ...param
        }

      }
      that.$ext.webSocket.webSocketSend(ORDER_EVALUATE_CMD, data)
    },
    editOrder ({ touchType }) {
      // 订单是否已结束
      if (this.orderDetail.consultStatus === 3) return
      switch (touchType) {
        case 3:
          this.$common.setKeyVal('chat', 'guideMode', 'department')
          break
        case 4:
          this.$common.setKeyVal('chat', 'guideMode', 'issue')
          break
        case 5:
          this.$common.setKeyVal('chat', 'guideMode', 'patient')
          break
        case 7:
          this.$common.setKeyVal('chat', 'guideMode', 'consultType')
        default:
      }
    },
    //查看大图
    viewImg(imgList) {
      uni.previewImage({
        urls: imgList,
        // #ifndef MP-WEIXIN
        indicator: 'number'
        // #endif
      });
    },
    scrollBottom () {
      console.log('滚动到底部')
      uni.pageScrollTo({
        scrollTop: 99999,
        duration: 0
      });
    },
    /**
     * 发送已读到WS
     */
    sendReadStatus(){
      const that = this
      let msgIds = []
      for (let i = 0; i < that.billingKey.length; i++) {
        let item = that.billingKey[i]
          if (item.readStatus==2 && !item.isItMe) {
            msgIds.push(item.id)
          }
      }
      if (msgIds.length === 0){
        return
      }
      const data =  {
        cmd: that.$constant.chat.READ_CMD,
        data: {
          businessType: 1,
          userId: this.chatItem.userId,
          chatUserId: this.chatItem.chatUserId,
          msgIds:msgIds,
          orderId: this.chatItem.orderId
        }

      }
      that.$ext.webSocket.webSocketSend(that.$constant.chat.READ_CMD, data)
    },
    // 异步获取元素
    getEl (getDiv) {
      return new Promise((resolve, reject) => {
        // #ifdef H5
        resolve(getDiv.getBoundingClientRect())
        // #endif

        // #ifndef H5
        if (getDiv.boundingClientRect) {
          getDiv.boundingClientRect(data => {
            resolve(data)
          }).exec()
        }
        // #endif
      })
    },

    // 判断DIV 是否隐藏
    isDivDisplay (el) {
        let ishidden = false
        // #ifdef H5
        if (el.style.display === 'none') {
          ishidden = true
        }
        // #endif

        // #ifndef H5
        // if (el.boundingClientRect) {
        //   el.boundingClientRect(data => {
        //     console.log('isDivDisplay------------------', data)
        //     // inputHeight = data.height
        //   }).exec()
        // }
        // #endif
        return ishidden
    },

    // 判断是否在可视区域 (位置信息)
    async isElementInViewportPos (getDiv) {
      var viewPortHeight = uni.getSystemInfoSync().windowHeight
      const elInfo = await this.getEl(getDiv)
      let top = 0
      let height = 0
      if (elInfo) {
        top = elInfo.top
        height = elInfo.height
      } else {
        return Promise.resolve(false)
      }
      viewPortHeight = viewPortHeight - this.bottomBoxHeight
      if (top >= 0) {
        return Promise.resolve({isShow: top <= viewPortHeight, isTop: false})
      } else {
        return Promise.resolve({isShow: (height + top) > 0, isTop: true})
      }
    },
    // 判断是否在可视区域
    async isElementInViewport (getDiv) {
      var viewPortHeight = uni.getSystemInfoSync().windowHeight
      const elInfo = await this.getEl(getDiv)
      let top = 0
      let height = 0
      if (elInfo) {
        top = elInfo.top
        height = elInfo.height
      } else {
        return Promise.resolve(false)
      }
      viewPortHeight = viewPortHeight - this.bottomBoxHeight
      console.log(viewPortHeight, top, height)
      if (top >= 0) {
        return Promise.resolve(top <= viewPortHeight)
      } else {
        return Promise.resolve((height + top) > 0)
      }
    },
    // 检查div 是否曝光
    async checkDivShow() {
      const that = this
      that.billingKey = []
      for (let i = 0; i < that.messageList.length; i++) {
        var item = that.messageList[i];
        if (item.readStatus == 2 && !(item.fromUserId + '' == this.chatItem.userId + '')) {

          // #ifdef H5
          var getDiv = document.getElementById('msg-' + item.hasBeenSentId)
          // #endif

          // #ifndef H5
          const query = uni.createSelectorQuery().in(this)
          var getDiv = query.select('#msg-' + item.hasBeenSentId)
          // #endif

          if (getDiv!=null){
            if (getDiv.id !== undefined || getDiv.boundingClientRect !== undefined) {
              let obj = {id: item.hasBeenSentId, readStatus: item.readStatus}
              // 曝光上报
              if (await this.isElementInViewport(getDiv) && !this.isDivDisplay(getDiv)) {
                var isExist = false;
                that.billingKey.some(function (item) {
                  if (item.id === obj.id) {
                    isExist = true;
                  }
                })
                if (!isExist) {
                  console.log(getDiv, obj)
                  that.billingKey.push(obj)
                }
              }
            }
          }
        }
      }
      console.log("that.billingKey:",that.billingKey)
      if (that.billingKey.length >0) {
        that.sendReadStatus()
      }
    },
    navtoGo(url, obj) {
      const parameter = obj || {}
      this.$navto.push(url, parameter)
    },
    //拼接消息 处理滚动
    async joinData() {
      const that = this
      if (this.messageListLoadingStatus !== 2) {
        //如果没有获取数据 即loading为false时，return 避免用户重复上拉触发加载
        return;
      }
      // that.loading = false;
      this.$common.setKeyVal('chat', 'messageListLoadingStatus', 1, false)
      this.loading = false
      this.getMessageData();
    },
    //处理滚动
    bindScroll(sel, duration = 0) {
      let top = this.contentBoxElInfo.top || 0
      const query = uni.createSelectorQuery().in(this);
      query
      .select(sel)
      .boundingClientRect(data => {
        uni.pageScrollTo({
          scrollTop: data && data.top - top,
          duration
        });
      })
      .exec();
    },
    //获取消息
    getMessageData() {
      const that = this
      console.log('this.formData--------------------------', this.formData, this)
      try {
        const data = {
          cmd: that.$constant.chat.GET_SINGLE_CHAT_CMD,
          data: {
            userId: that.chatItem.userId,
            // orderId: that.chatItem.orderId,
            chatUserId: that.chatItem.chatUserId,
            lastMsgId: that.$validate.isNull(that.messageList) ? '' : that.messageList[0].id,
            pageSize: that.formData.limit
          }
        }
        that.$ext.webSocket.webSocketSend(that.$constant.chat.GET_SINGLE_CHAT_CMD, data)
      } catch (err) {
        console.log('err---------------------', err)
      }

    },
    //用户触摸屏幕的时候隐藏键盘
    touchstart() {
      uni.hideKeyboard();
    },
    isShowTime(index,protoMessages){
      if (index === 0) return true
       var msgTime = protoMessages[index].updateTime;
       if(index > 0){
           var preProtoMessage = protoMessages[index - 1];
           var preMsgTime = preProtoMessage.updateTime;
           if(msgTime - preMsgTime > ( 5 * 60 * 1000)){
               return true;
           }
       }
       return false;
    },
  },
  filters: {
    getTimeStringAutoShort2(timestamp){
        try {
            timestamp = Number(timestamp) ? Number(timestamp) : timestamp
        } catch (err) {

        }
        return TimeUtils.getTimeStringAutoShort2(new Date(timestamp).getTime(),true);
    }
  }
}
</script>
<style lang="scss" scoped>
@import './message.scss';
.dialog-title{
	    font-size: 36upx;
	    font-weight: 550;
	    line-height: 2;
	    display: flex;
	    align-items: center;
	    justify-content: center;
	    padding-top: 33upx;
	    padding-bottom: 58upx;
}
.dialog-btns{
	display:flex;
	padding:0 20upx;
	.dialog-btn{
		flex:1;
		height: 80upx;
		background-color:#eeeeee;
		font-size:32upx;
		display:flex;
		align-items:center;
		justify-content:center;
		border-radius:50upx
	}
	.dialog-btn.active{
		background-color:#00d29d;
		color:#fff
	}
	.dialog-space{
		width: 20upx;
		height: 1upx
	}
}
</style>
