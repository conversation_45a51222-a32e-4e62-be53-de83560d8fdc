<template>
  <view
    class="formsize"
    :style="{
      paddingTop: headerTop + 'px'
    }"
  >
    <!-- #ifndef MP-ALIPAY -->
    <title-header :headerobj="headerobj" @init="initHeader"></title-header>
    <!-- #endif -->
    <view class="image-box">
      <image :src="pharmacyType == 1 ? urlImg : bannerImg" mode="" class="image" mode="widthFix"></image>
    </view>
    <!-- <view class='form-box'> -->
    <view class="form-box" v-if="pharmacyType == 1">
      <!-- <template v-for="item in addFlag?config:updateConfig"> -->
      <template v-for="item in config">
        <template v-if="item.type === 'input'">
          <title-input-two horizontal :disabled="item.disabled" :config="item" v-model="regForm[item.name]"></title-input-two>
        </template>
        <template v-else-if="item.type == 'select'">
          <lv-title-selector-input :disabled="item.disabled" :config="item" :cData="regForm[item.name]" @updateForm="updateForm" />
        </template>
      </template>

      <view class="title-input d-flex">
        <view class="l-l" :style="{ color: defaultConfig.titleColor }" :class="defaultConfig.titleClass">
          {{ defaultConfig.label }}
          <text class="star" v-if="defaultConfig.required">*</text>
        </view>
        <view class="l-r">
          <!-- 123456 -->
          <view class="select-item" @click="toggleActive(1)">
            <image :src="cryico" class="select-item-ico" v-if="regForm.satisficingStatus == 2"></image>
            <image :src="cryico_a" class="select-item-ico" v-else></image>
            <text class="select-item-txt" :class="regForm.satisficingStatus == 1 ? 'active' : ''">不满意</text>
          </view>

          <view class="select-item mgl20" @click="toggleActive(2)">
            <image :src="laughico" class="select-item-ico" v-if="regForm.satisficingStatus == 1"></image>
            <image :src="laughico_a" class="select-item-ico" v-else></image>
            <text class="select-item-txt" :class="regForm.satisficingStatus == 2 ? 'active' : ''">满意</text>
          </view>
        </view>
      </view>
    </view>

    <view class="form-box2">
      <form-template
        :ptype="3"
        :noloading="noloading"
        :height="height"
        :extra="extra"
        :extraForm="extraForm"
        :extraUpdate="extraUpdate"
        :updatecount="updatecount"
        :updatepage="updatepage"
        :stepid="stepid"
        :visitid="activityid"
        :userDisabled="logid ? true : false"
        :submitBtnText="pharmacyType == 1 ? '提交' : '提交评价'"
        :parentbusinessid="parentBusinessId"
        @returnFn="returnFn"
        :isone="false"
        :submititemlogid="logid"
        :nosubmit="logid"
        :pharmacyType="pharmacyType"
      ></form-template>
    </view>
  </view>
</template>

<script>
import formTemplate from '../../components/form-template/index.vue';
import TitleInputTwo from '@/components/business/module/v2/title-input/index';
import TitleSelector from '@/components/business/module/v1/title-selector/index.vue';

import lvTitleSelectorInput from '../../components/lv-title-selector-input/index.vue';
// import TitleInput from '@/modules/activity/components/title-input/index';
import { getQueryObject } from '@/utils/index';
import { mapState } from 'vuex';
import titleHeader from '@/modules/activity/components/title-header/index.vue';
// const $static_ctx = 'http://localhost:3000/'
export default {
  components: {
    formTemplate,
    TitleInputTwo,
    TitleSelector,
    lvTitleSelectorInput,
    titleHeader
  },
  computed: {
    ...mapState('user', {
      recordUserInfo: (state) => state.recordUserInfo, // 当前登录用户信息
      fansRecord: (state) => state.fansRecord,
      accountId: (state) => state.accountId,
      curSelectUserInfo: (state) => state.curSelectUserInfo
    })
  },
  data() {
    return {
      pageQuery: {},
      // #ifndef MP-ALIPAY
      headerTop: 55,
      // #endif
      // #ifdef MP-ALIPAY
      headerTop: 0,
      // #endif
      // 医院服务评价
      headerobj: {
        headBgColor: '#0bdaa6',
        titleType: 'txt',
        titleTxt: '医院服务评价',
        currentIndex: 0,
        contentColor: '#fff'
        // borderColor:"#000"
      },

      cryico: this.$static_ctx + 'image/business/icon-im-cry.png',
      cryico_a: this.$static_ctx + 'image/business/icon-im-cry-a.png',
      laughico_a: this.$static_ctx + 'image/business/icon-im-laugh-a.png',
      laughico: this.$static_ctx + 'image/business/icon-im-laugh.png',

      urlImg: this.$static_ctx + 'image/business/im-diagnosis-bg.png',
      bannerImg: this.$static_ctx + 'image/business/drugBanner.jpg',

      extraUpdate: 0,
      submitItemLogId: null,

      extra: [{ label: '我们期待聆听您的感受，帮助我们做得更好', type: 'textarea', nextRequest: true, required: false, name: 'feedback', placeholder: '请输入您的反馈意见' }],
      extraForm: {
        feedback: ''
      },
      templateData: {},
      regForm: {
        satisficingStatus: 1
      },
      activityid: null,
      updatepage: 0,
      noloading: false,
      mainId: null,
      // config:[],
      config: [
        {
          label: '医院名称',
          border: false,
          bdb: false,
          type: 'select',
          titleClass: 'font36',
          nextRequest: true,
          required: true,
          array: [],
          name: 'installId',
          placeholder: '可输入关键字快速匹配选择项'
        },
        {
          label: '科室',
          border: false,
          bdb: false,
          bdt: false,
          type: 'select',
          titleClass: 'font36',
          nextRequest: true,
          required: true,
          array: [],
          name: 'tagId',
          placeholder: '可输入关键字快速匹配选择项'
        },
        { label: '医生', border: false, bdb: false, bdt: false, type: 'input', titleClass: 'font36 bold', nextRequest: true, required: false, name: 'doctorName' }
      ],
      updateConfig: [
        { label: '医院名称', border: false, bdb: false, bdt: false, type: 'input', titleClass: 'font36', nextRequest: true, required: true, name: 'installId' },
        { label: '科室', border: false, bdb: false, bdt: false, type: 'input', titleClass: 'font36', nextRequest: true, required: true, name: 'tagId' },
        { label: '医生', border: false, bdb: false, bdt: false, type: 'input', titleClass: 'font36 bold', nextRequest: true, required: false, name: 'doctorName' }
      ],
      defaultConfig: {
        label: '就诊服务',
        border: true,
        type: 'select',
        titleClass: 'font36',
        array: [
          {
            label: '不满意',
            id: 1
            // key:1,
          },
          {
            label: '满意',
            id: 2
            // key:2,
          }
        ],
        name: 'satisficingStatus',
        titleClass: 'font36',
        nextRequest: true,
        required: true
      },
      regForm: {
        doctorName: '',
        installId: '',
        tagId: '',
        satisficingStatus: 2
      },
      logid: null,
      shareTitle: '医院服务评价',
      installName: '', //list 传过来的医院名称
      officeName: '', //list 传过来的科室名称
      addFlag: false,
      pharmacyType: null //类型 1诊后点评 2用药点评
    };
  },
  onShow() {
    this.noloading = true;
    this.$nextTick(() => {
      setTimeout(() => {
        this.noloading = false;
      }, 500);
    });
  },

  onShareAppMessage(res) {
    const that = this;
    // if (res.from === 'button') {// 来自页面内分享按钮
    //   console.log(res.target)
    // }
    return {
      title: this.shareTitle, //分享的名称
      path:
        'modules/activity/pages/diagnosis/add?query=' +
        encodeURIComponent(
          JSON.stringify({
            id: this.activityid
          })
        ),
      mpId: this.$appId, //此处配置微信小程序的AppId
      imageUrl: this.$static_ctx + 'image/business/im-diagnosis-bg.png',
      success: () => {
        // this.shareRecord()
      }
    };
  },
  //分享到朋友圈
  onShareTimeline(res) {
    const that = this;
    return {
      title: this.shareTitle,
      path:
        'modules/activity/pages/diagnosis/add?query=' +
        encodeURIComponent(
          JSON.stringify({
            id: this.activityid
          })
        ),
      imageUrl: this.$static_ctx + 'image/business/im-diagnosis-bg.png',
      success: () => {
        // this.shareRecord()
      }
    };
  },

  onLoad(options) {
    console.log('options', options);
    // this.$ext.wechat.getOpenId()
    // let pages = getCurrentPages();
    // console.log(pages[0])
    const query = this.$validate.isNull(this.$Route.query) ? options : this.$Route.query;
    console.log('query------------', query);
    this.installName = query.installName;
    this.officeName = query.officeName;
    this.addFlag = query.addFlag;
    if (!this.addFlag) {
      this.config = this.updateConfig;
    }
    this.pharmacyType = query.type;
    this.headerobj.titleTxt = this.pharmacyType == 1 ? '医院服务评价' : '用药评价';
    const that = this;
    // 记录页面参数
    this.pageQuery = query;
    if (this.$validate.isNull(query.id)) {
      let params = decodeURIComponent(query.scene);
      console.log('params:====', params);
      query.id = getQueryObject(params).id;
      if (this.$validate.isNull(query.id)) {
        that.$uniPlugin.toast('参数异常');
        return;
      }
    }
    options = query;

    console.log('options');

    if (options && options.id) {
      this.activityid = options.id;
    }
    if (options && options.logid) {
      this.logid = options.logid;
      for (let i = 0; i < this.config.length; i++) {
        this.config[i].disabled = true;
      }
      for (let i = 0; i < this.extra.length; i++) {
        this.extra[i].disabled = true;
      }
    } else {
      this.logid = null;
    }

    this.$nextTick(() => {
      this.updatepage += 1;
    });

    this.$uniPlugin.loading('加载中', true);

    Promise.all([this.gethospitallistAllHospital(), this.getfindByParentId()])
      .then((result) => {
        console.log(result); //['成功了', 'success']
        if (this.logid) {
          this.gethospitalserviceevaluatelogqueryone();
        } else {
          this.getpacketRecordqueryPage();
        }
      })
      .catch((error) => {
        console.log(error);
      });

    // #ifdef MP-WEIXIN
    this.handleClickTrack('OperationDetailsPageView');
    // #endif

    // this.getDetail(this.currentid);
    // console.log('mound')
  },
  onUnload() {
    // #ifdef MP-WEIXIN
    this.handleClickTrack('EndOperationDetailsPageView');
    // #endif
  },
  methods: {
    // #ifdef MP-WEIXIN
    handleClickTrack(type) {
      let pages = getCurrentPages();
      let current = pages[pages.length - 1]; // 获取到当前页面的信息
      let pageInfo = (__wxConfig.page && __wxConfig.page[`${current.route}.html`]) || ''; // 内部属性
      getApp().globalData.sensors.track(type, {
        page_name: pageInfo?.window?.navigationBarTitleText || '',
        first_operation_name: pageInfo?.window?.navigationBarTitleText || '',
        second_operation_name: ''
      });
    },
    // #endif
    // shareRecord () {
    //     const param = {
    //         accountId: this.accountId,
    //         businessType: 5, // 业务类型:1-横幅，2-外部服务应用，3-企业微信群，4-圈子，5-帖子，6-帖子评论
    //         businessId: this.activityid,
    //         source: 1, // 来源：1-真实用户，2-马甲
    //         type: 13, // 类型：1-访问，2-点击，3-长按，4-关注，5-取关，6-点赞，7-收藏，8-删除评论，9-取消点赞，10-取消收藏，11-阅读 13-分享
    //     }
    //     this.$api.community.applicationoperatelogInsert(param)
    // },
    initHeader(height) {
      this.headerTop = height;
    },
    toggleActive(idx) {
      this.regForm.satisficingStatus = idx;
    },
    updateForm(obj) {
      console.log('obj.value', obj);
      // if(obj.key === )
      if (obj.value) {
        this.regForm[obj.key] = obj.value.id;
        if (obj.key == 'installId') {
          this.regForm.installIdName = obj.value?.label;
        }
        if (obj.key == 'tagId') this.regForm.tagIdName = obj.value?.label;
      }
    },
    // 获取默认安装单位
    getpacketRecordqueryPage() {
      this.$api.activity
        .packetRecordGetNewPacketLog({
          // condition:{
          accountId: this.$common.getKeyVal('user', 'accountId', true)
          // accountId:'822516292530475013'
          // },
        })
        .then((res) => {
          if (res.code === 0) {
            const data = res.data;
            if (data instanceof Object) {
              this.regForm.installId = data.installId;
            }
          }
          this.$uniPlugin.hideLoading();
        })
        .catch((e) => {
          this.$uniPlugin.hideLoading();
        });
    },
    // 获取详情
    gethospitalserviceevaluatelogqueryone(id) {
      this.$api.activity
        .hospitalserviceevaluatelogqueryone({
          id: this.logid // 安装单位类型(1医院,2药店,3超市)
        })
        .then((res) => {
          if (res.data && res.data != '') {
            for (let key in this.regForm) {
              this.regForm[key] = res.data[key];
              this.regForm.installId = this.installName;
              this.regForm.tagId = this.officeName;
            }

            this.extraForm.feedback = res.data.feedback;
            this.$nextTick(() => {
              this.extraUpdate += 1;
            });
          }
          this.$uniPlugin.hideLoading();
        });
    },
    // 获取医院
    async gethospitallistAllHospital() {
      await this.$api.activity
        .hospitallistAllHospital({
          installType: [1] // 安装单位类型(1医院,2药店,3超市)
        })
        .then((res) => {
          let idx = this.config.findIndex((item) => item.name === 'installId');
          if (idx != -1) {
            res.data.forEach((item) => {
              item.label = item.name;
              item.value = item.id;
            });
            this.config[idx].array = res.data;
          }
        });
    },
    async getfindByParentId() {
      await this.$api.common
        .findByParentId({
          parentId: 120003
        })
        .then((res) => {
          let idx = this.config.findIndex((item) => item.name === 'tagId');
          if (idx != -1) {
            res.data.forEach((item) => {
              item.label = item.codeName;
              item.value = item.id;
            });
            this.config[idx].array = res.data;
          }
        });
    },

    async saveFn(additionalForm) {
      return new Promise((resolve, reject) => {
        let param = {
          accountId: this.$common.getKeyVal('user', 'accountId', true),
          doctorName: this.regForm.doctorName, // 	医生名称
          // "feedback":, // 反馈
          installId: this.regForm.installId, // 医院id
          tagId: this.regForm.tagId, // 科室id
          // "id": 0,
          mainId: this.activityid,
          // writeFormId:this.writeFormId,
          feedback: additionalForm?.feedback, // 反馈意见
          userName: additionalForm.name,
          satisficingStatus: this.regForm.satisficingStatus, // satisficingStatus	服务满意度:1-满意，2-不满意
          type: 1

          // "status": 0,
          // "tagId": 0,
          // "tenantId": 0,
          // "updateTime": "",
          // "updateUser": 0,
          // "writeFormId": 0
        };
        this.$api.activity
          .hospitalserviceevaluateloginsert(param)
          .then((res) => {
            resolve(res);
            // this.noloading = true;

            // this.$nextTick(() => {
            //   setTimeout(() => {
            //     this.noloading = false;
            //     this.$navto.push('diagnosisComment')
            //   },500)
            // })
          })
          .catch((e) => {
            resolve({});
          });
      });
    },
    // 用药评价提交方法
    async pharmacySaveFn() {
      return new Promise((resolve, reject) => {
        let param = {
          accountId: this.$common.getKeyVal('user', 'accountId', true),
          mainId: this.activityid,
          type: 2
        };
        this.$api.activity
          .hospitalserviceevaluateloginsert(param)
          .then((res) => {
            resolve(res);
            // this.noloading = true;

            // this.$nextTick(() => {
            //   setTimeout(() => {
            //     this.noloading = false;
            //     this.$navto.push('diagnosisComment')
            //   },500)
            // })
          })
          .catch((e) => {
            resolve({});
          });
      });
    },
    async returnFn(obj) {
      console.log(obj.param, 'param777');
      if (!this.curSelectUserInfo || !this.curSelectUserInfo.centerUserId) {
        let pageQuery = this.pageQuery || {};
        this.$navto.push('Login', {
          formPage: 'diagnosisCommentAdd',
          formPageParams: encodeURIComponent(
            JSON.stringify({
              // userParam: obj,
              ...pageQuery,
              loginIn: true, // 触发登陆，登陆之后回来
              isBackVisible: true
            })
          )
        });
        return;
      }
      // if(!this.regForm.installId){
      //   return this.$uniPlugin.toast(this.config[i].label + '不能为空');
      // }
      // console.log('this.regForm',this.regForm)
      // return
      if (this.pharmacyType == 1) {
        for (let i = 0; i < this.config.length; i++) {
          if (this.config[i].required) {
            if (this.regForm[this.config[i].name] === '') {
              this.noloading = true;

              this.$nextTick(() => {
                setTimeout(() => {
                  this.noloading = false;
                }, 500);
              });
              return this.$uniPlugin.toast(this.config[i].label + '不能为空');
            }
          }
        }
      }

      let param = obj.param;
      let templateId = obj.templateId;
      let additionalForm = obj.additionalForm;
      const { name, userId } = this.recordUserInfo;
      additionalForm.name = name;
      if (!additionalForm.name || additionalForm.name === '') {
        additionalForm.name = this.fansRecord.nickName;
      }

      // const extendValueForm = obj.extendValueForm;

      if (param.patientInfo instanceof Object) {
        // param.patientInfo.physicianUserId = userId
        const curSelectStore = this.$common.getKeyVal('user', 'curSelectStore', true);
        let recordId = curSelectStore && curSelectStore.userTenantRecordList[0] ? curSelectStore.userTenantRecordList[0].recordId : null;

        if (!recordId) {
          return this.$uniPlugin.toast('档案为空,不能保存');
        }

        param.patientInfo.physicianUserId = recordId;
      }
      delete param.caseCollectSubmitLogType;

      let res;
      if (this.pharmacyType == 1) {
        // 保存模板
        res = await this.saveFn(additionalForm);
      } else {
        res = await this.pharmacySaveFn();
      }

      if (!res.data || res.code !== 0) {
        this.noloading = true;

        this.$nextTick(() => {
          setTimeout(() => {
            this.noloading = false;
            // this.$navto.push('diagnosisComment')
          }, 500);
        });
        return;
      }

      param = {
        ...param,
        userName: name,
        accountId: this.$common.getKeyVal('user', 'accountId', true),
        userId: userId,
        openId: this.$common.getCache('openId'),
        mainId: templateId,
        // activityType:this.activitytype,
        businessId: res.data.id,
        activityId: this.activityid,
        businessType: 5 // 业务类型:1-dm，2-私域,3-病例征集(流程提交记录),4-回访(流程提交记录)
      };
      if (!param.userName || param.userName == '') {
        param.userName = this.fansRecord.nickName;
      }
      //  console.log('this.fansRecord',this.fansRecord,param)
      const that = this;
      // hospitalserviceevaluateloginsert
      that.$api.activity
        .submitformtemplatewrite(param)
        .then(async (resp) => {
          if (resp.code === 0) {
            that.$uniPlugin.toast('提交成功');
            this.mainId = resp.data.activityId;
            this.writeFormId = resp.data.id;
            // #ifdef MP-WEIXIN
            if (this.pharmacyType && this.pharmacyType == 1) {
              getApp().globalData.sensors.track('PostConsultationReview', {
                hospital_id: that.regForm?.installId,
                hospital_name_xhl: that.regForm?.installIdName,
                department: that.regForm?.tagIdName,
                doctor: that.regForm?.doctorName,
                consultation_service: that.regForm?.satisficingStatus == 2 ? '满意' : '不满意',
                consultation_environment: `${param?.formWriteValueDTOS[0].value}星`,
                registration_convenience: `${param?.formWriteValueDTOS[1].value}星`,
                bag_collection_convenience: `${param?.formWriteValueDTOS[2].value}星`,
                service_attitude: `${param?.formWriteValueDTOS[3].value}星`,
                diagnostic_level: `${param?.formWriteValueDTOS[4].value}星`,
                feedback_opinion: additionalForm?.feedback
              });
            } else {
              getApp().globalData.sensors.track('MedicationEvaluation', {
                medicine_id: this?.activityid,
                evaluation_content: param?.formWriteValueDTOS[0]
              });
            }
            // #endif
          }
          await this.subscribeMessage(res.data.id);
          this.noloading = true;
          this.$nextTick(() => {
            setTimeout(() => {
              this.noloading = false;
              // 跳往订阅消息页面
              console.log(resp.data);
              this.$navto.push('diagnosisSubscribemessage', { id: res.data.id });
              // this.$navto.push('diagnosisComment')
            }, 500);
          });

          // if(obj.next){
          //   this.$emit('next')

          //   if(this.emainId && this.back){
          //      this.$navto.back(1);
          //   }
          // }

          // this.getDetail(this.id);
        })
        .catch((e) => {
          // console.log('jkkk错误')
          this.noloading = true;

          this.$nextTick(() => {
            setTimeout(() => {
              this.noloading = false;
            }, 500);
          });
        });
    },
    // 重新发起订阅
    async resetSubscribeMessage(id) {
      await this.subscribeMessage(id);
      this.noloading = true;
      this.$nextTick(() => {
        setTimeout(() => {
          this.noloading = false;
          // 跳往订阅消息页面
          console.log(resp.data);
          this.$navto.push('diagnosisSubscribemessage', { id: res.data.id });
          // this.$navto.push('diagnosisComment')
        }, 500);
      });
    },
    hideBtnLoading() {
      this.noloading = true;
      this.$nextTick(() => {
        setTimeout(() => {
          this.noloading = false;
        }, 500);
      });
    },
    async subscribeMessage(businessId) {
      const { centerUserId = '' } = this.curSelectUserInfo || {};
      // 获取openid
      let openId = await this.$ext.wechat.getOpenId();
      let is = await this.$uniPlugin.subscribeMessage(this.$constant.system.diagnosisTmplIds, false);
      if (!is) {
        this.hideBtnLoading();
      }
      const subscribeMessageRes = await this.requestSubscribeMessage(this.$constant.system.diagnosisTmplIds, businessId);
      const logParamList = Object.keys(subscribeMessageRes)
        .filter((key) => {
          return this.$constant.system.diagnosisTmplIds.includes(key);
        })
        .map((key) => {
          return {
            appId: this.$appId,
            templateId: key,
            openId: openId,
            subscribeStatus: subscribeMessageRes[key],
            businessType: 5, // 诊后点评
            businessId,
            accountId: this.accountId,
            userId: centerUserId
          };
        });
      await this.$api.common.wxsubscribemessagelogInsertBatch({ wxSubscribeMessageLogList: logParamList }).catch((e) => {
        this.$uniPlugin.toast('订阅失败');
        this.hideBtnLoading();
      });

      // this.$uniPlugin.modal('返回提交记录', '', {
      //   showCancel: true, // 是否显示取消按钮，默认为 true
      //   cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
      //   cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
      //   confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
      //   confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
      //   fn: async (n) => {
      //     if (n) {
      //       this.$navto.replace('diagnosisComment')
      //     }
      //   }
      // })
    },
    // 消息订阅
    requestSubscribeMessage(tmplIds, businessId) {
      return new Promise((resolve, reject) => {
        try {
          this.$uniPlugin.requestSubscribeMessage(tmplIds, (res) => {
            let status = true;
            tmplIds.forEach((item) => {
              if (res[item.toString()] !== 'accept') status = false;
            });
            if (status) {
              this.isShow = false;
              this.$uniPlugin.toast('订阅成功');
              // #ifdef MP-WEIXIN
              getApp().globalData.sensors.track('Subscription', {
                content_belong_circle: '',
                function_name: '诊后点评',
                subscription_type: '一次性订阅'
              });
              // #endif
              resolve(res);
            } else {
              this.isShow = true;
              this.$uniPlugin.toast('订阅失败');
              resolve(res);
            }
          });
          this.$uniPlugin.hideLoading();
        } catch (err) {
          // this.$uniPlugin.toast('订阅失败,请重试')
          this.hideBtnLoading();
          uni.showModal({
            title: '温馨提示',
            content: '网络异常，是否重新发起订阅？',
            success(res) {
              if (res.confirm) {
                this.resetSubscribeMessage(businessId);
                // console.log('用户点击确定')
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          });
          reject(err);
        }
      });
    }
    // getDetail(id) {
    //   const that = this
    //   that.$api.activity.hospitalserviceevaluatequeryone({ id: id }).then(res => {
    //     res.data.pushTimeText = this.$common.formatDate(new Date(res.data.createTime), 'yyyy-MM-dd')
    //     that.regForm = res.data
    //     that.$api.activity.queryBusinessTemplateDTOList({ templateIds: that.regForm.templateId }).then(res => {
    //       this.templateData = res.data
    //       that.openTemplate = true
    //     })
    //   })
    // }
  }
};
</script>

<style lang="scss" scoped>
.mgl20 {
  margin-left: 20upx;
}
.select-item-ico {
  width: 50upx;
  height: 50upx;
}
.select-item {
  background-color: #f9f9f9;
  // width: 30%;
  padding: 0 20upx;
  border-radius: 10upx;
  overflow: hidden;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  .select-item-txt {
    margin-left: 10upx;
    font-weight: 550;
    color: #7c7c7c;
  }
  .select-item-txt.active {
    color: $topicC;
  }
}
.d-flex {
  display: flex;
  align-items: center;
}
.title-input {
  background-color: #ffffff;
  .l-l {
    line-height: 88upx;
    color: #333333;
    font-weight: 600;
    font-size: 30upx;
  }
  .l-l.font36 {
    font-size: 36upx;
  }
  .l-r {
    // margin-bottom: 5px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 10upx;
    // padding-bottom: 20rpx;
    flex: 1;
    height: 80upx;
  }
}
.star {
  color: #f85e4c;
  padding-top: 6upx;
  font-size: 32upx;
  display: inline-block;
  margin-left: 10rpx;
}
.formsize {
  background-color: #f1f1f1;
  // view {
  //   font-size: 36upx;
  // }
}
.form-box {
  padding: 20upx 20upx 0 20upx;
  margin: 20upx;
  background-color: #fff;
  border-radius: 10upx;
  padding-bottom: 10upx;
  // overflow: hidden;
}
.form-box2 {
  margin: 20upx;
  overflow: hidden;
  border-radius: 10upx;
  background-color: #fff;
}
.image-box {
  margin: 20upx;
  overflow: hidden;
  border-radius: 10upx;
  background-color: #fff;
  box-sizing: border-box;
}
.image {
  vertical-align: middle;
  width: 100%;
}
</style>
