<template>
  <view class="frame-page" :style="{ height: screenHeight + 'px' }">
    <view class="uni-flex uni-column">
      <main class="content">
        <slot name="content"></slot>
      </main>
      <!-- 原加载方案 -->
      <!-- <main class="content" v-if="pageNetworkStatus">
         <slot name="content"></slot>
       </main>
       <main class="content" v-if="!pageNetworkStatus">
         <div class="network-error-panel">
           <image mode="scaleToFill" :src="iconNetworkErrorPng"/>
           <div>糟糕，网络已断开了</div>
           <button type="default" @tap="submitBtn()">重新刷新</button>
         </div>
       </main> -->
    </view>
  </view>
</template>
<!--页面框架组件-->
<script>
import { mapState } from 'vuex';
export default {
  name: 'Page',
  components: {},
  computed: {
    isShow() {
      if (this.headerObj) {
        return this.headerObj.isShow;
      }
      return false;
    },
    ...mapState('navigation', {
      headerObj: (state) => state.headerObj
    }),
    ...mapState('system', {
      networkStatus: (state) => state.networkStatus
    })
  },

  data() {
    return {
      pageNetworkStatus: true,
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      file_ctx: this.file_ctx,
      screenHeight: 0,
      pathParams: {
        redirect: '',
        redirectParams: ''
      },
      timer: undefined,
      iconNetworkErrorPng: this.file_ctx + 'static/image/business/hulu-v2/icon-network-error.png',

      // 防止重复弹窗刷新弹窗
      hasShownNetworkModal: false
    };
  },
  watch: {
    networkStatus(newVal, oldVal) {
      // console.log('网络变化了....', newVal);
      if (!newVal) {
        this.netWorkStatusChange();
      }
    }
  },
  mounted() {
    this.screenHeight = uni.getSystemInfoSync().windowHeight;
    this.pageNetworkStatus = this.networkStatus;
  },
  methods: {
    submitBtn() {
      if (!this.networkStatus) {
        this.$uniPlugin.toast('您的网络好像掉线了，请稍后再试');
      } else {
        this.$uniPlugin.toast('网络已连接，请稍等');
      }
      this.timer && clearTimeout(this.timer);
      this.timer = setTimeout(() => {
        this.pageNetworkStatus = this.networkStatus;
        this.$navto.replace(this.pathParams.redirect, this.pathParams.redirectParams);
      }, this.$constant.noun.delayedOperationTime1000);
    },
    // 检查当前网络
    checkNetwork() {
      return new Promise((resolve) => {
        uni.getNetworkType({
          success: function (res) {
            const poorNetwork = ['none', 'unknown'];
            resolve(!poorNetwork.includes(res.networkType));
          },
          fail: () => resolve(false)
        });
      });
    },
    showNetworkModal() {
      this.$uniPlugin.modal('网络异常', '当前网络不可用，是否重新加载？', {
        showCancel: false, // 是否显示取消按钮，默认为 true
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if (n) {
            const available = await this.checkNetwork();
            if (available) {
              // 网络恢复，刷新页面（可自定义）
              this.hasShownNetworkModal = false;
              this.reloadPage();
            } else {
              // 继续显示弹窗
              this.$uniPlugin.toast('当前网络不可用!', true);
              setTimeout(() => {
                this.showNetworkModal(); // 重新弹出
              }, 2100);
            }
          }
        }
      });
    },
    reloadPage() {
      // 重新跳转当前页面（带参数）
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const route = currentPage.route;
      const options = currentPage.options || {};
      const queryString = Object.entries(options)
        .map(([k, v]) => `${k}=${v}`)
        .join('&');
      const isTabBarPage = ['/pages/index/index', '/pages/personal/index', '/pages/circle-home/index', '/pages/post-message/index', '/pages/news/index']; // 你的 tabbar 页面路径
      if (isTabBarPage.includes('/' + route)) {
        // 如果是 TabBar 页面
        uni.reLaunch({
          url: '/' + route + (queryString ? `?${queryString}` : '')
        });
      } else {
        uni.redirectTo({
          url: '/' + route + (queryString ? `?${queryString}` : '')
        });
      }
    },
    // 监听网络状态
    netWorkStatusChange() {
      const that = this;
      if (!this.hasShownNetworkModal) {
        this.hasShownNetworkModal = true;
        // 弹出提示
        this.showNetworkModal();
      }
    }
  },
  props: {
    type: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    }
  },
  onReady() {
    const query = this.$Route?.query;
    const path = this.$Route?.path;
    this.pathParams.redirect = path;
    this.pathParams.redirectParams = query;
  }
};
</script>

<style lang="scss" scoped>
.frame-page {
  height: 100%;
  width: 100%;
  height: 100%;
  background: $bgColor;
  .uni-flex {
    width: 100%;
    height: 100%;
    .nav-top-set {
      margin-top: 88upx !important;
    }
    .content {
      background-color: #f7f7f7;
      height: 100%;
    }
  }
  .network-error-panel {
    text-align: center;
    padding-top: 40%;
    image {
      width: 340upx;
      height: 310upx;
    }
    button {
      width: 55%;
      margin-top: 60upx;
      background: #8bc34a;
      color: white;
      border: none;
    }
  }
}
</style>
