<template>
    <view
      class="content-box"
      @touchstart="touchstart"
      id="content-box"
    >
        <!-- 背景图- 定位方式 -->
        <view class="content-box-loading" v-if="loading">
            <view class="icon-loading"></view>
        </view>
        <view class="message" v-for="(item, index) in messageList" :key="item.hasBeenSentId"
                :id="`msg-${item.hasBeenSentId}`">
            <view v-if="isShowTime(index,messageList) && ![6,8].includes(item.touchType)" class="time"><text class="time-text">{{item.updateTime | getTimeStringAutoShort2}}</text></view>
            <view
              class="message-item "
              :class="item.isItMe ? 'right' : 'left'"
              v-if="![6,8].includes(item.touchType)"
            >
                <image class="img avatar" :src="item.fromUserHeadImg ? file_ctx + item.fromUserHeadImg : defaultAvatar"></image>
                <!-- msgType = 1 文本 -->
                <view class="content" v-if="item.msgType == 1">{{ item.content }}</view>
                <!-- msgType = 2 图片 -->
                <view
                    class="content msgType2"
                    v-else-if="item.msgType == 2"
                    @tap="viewImgs(item,index)"
                >
                    <image :src="file_ctx + item.content" class="img" mode="widthFix"></image>
                </view>

                <!-- 视频 -->
                <view class="content msgType2" v-else-if="item.msgType === 3">
                    <video style="height: 200px;" :src="file_ctx + item.content" controls class="img" />
                </view>
                <!-- 文件 -->
                <view class="content" v-else-if="item.msgType === 4">
                    <view
                        style="font-size: 28rpx;"
                        @tap="uploadFile(file_ctx + item.content)"
                    >
                        <template v-if="item.desc">
                            {{ JSON.parse(item.desc)[0].name }}
                        </template>
                    </view>
                </view>
                <!-- H5卡片 -->
                <view v-else-if="item.msgType === 5" class="content card" @tap="clickCard(item)">
                    <template v-if="item.msgContent">
                        <view class="card-content">
                            <view class="card-content-title">{{ item.text }}</view>
                            <view class="card-content-desc">{{ item.desc }}</view>
                        </view>
                        <image :src="file_ctx + item.attach" alt="" class="card-img">
                    </template>
                </view>
                <!-- 小程序卡片 -->
                <view v-else-if="item.msgType === 6" class="content card" @tap="clickCard(item)">
                    <template v-if="item.msgContent">
                        <view class="card-content">
                            <view class="card-content-title">{{ item.text }}</view>
                            <view class="card-content-desc">{{ item.desc }}</view>
                        </view>
                        <image :src="file_ctx + item.attach" alt="" class="card-img">
                    </template>
                </view>

                <!-- msgType = 8 业务卡片 -->
                <view
                  v-else-if="item.msgType == 8"
                  class="content"
                >
                  <view class="content-title">{{ item.text }}</view>
                  <view class="content-card">
                    <template v-if="item.desc">
                      <view class="content-card-item" v-if="item.desc.patientInfoVo">
                        <view class="content-card-key">咨询人：</view>
                        <view class="content-card-value">{{ `${item.desc.patientInfoVo.name} | ${item.desc.patientInfoVo.gender === 1 ? '男' : item.desc.patientInfoVo.gender === 2 ? '女' : '未知' } | ${item.desc.patientInfoVo.age||''}`}}</view>
                      </view>
                      <view class="content-card-item" v-if="item.desc.gfConsultTypeName">
                        <view class="content-card-key">咨询类型：</view>
                        <view class="content-card-value">{{ item.desc.gfConsultTypeName }}</view>
                      </view>
                      <view class="content-card-item" v-if="item.desc.gfDepartmentName">
                        <view class="content-card-key">咨询问题：</view>
                        <view class="content-card-value">{{ item.desc.gfDepartmentName }}</view>
                      </view>
                      <view class="content-card-item" v-if="item.desc.gfIssue">
                        <view class="content-card-key">咨询内容：</view>
                        <view class="content-card-value">{{ item.desc.gfIssue }}</view>
                      </view>
                      <view class="content-card-item" v-if="item.desc.gfAttachs">
                        <view class="content-card-key" style="width: 160rpx;">图片：</view>
                        <image @tap="viewImg([file_ctx + item.desc.gfAttachs])" class="content-card-value" mode="aspectFit" :src="file_ctx + item.desc.gfAttachs"></image>
                      </view>
                    </template>
                  </view>
                </view>
                <view class="icon-loading msg-loading" v-if="item.msgCloudStatus === 2"></view>
                <!-- <view class="read-status-panel" :class="item.readStatus == 1 ? 'icon-yijianfankui-d-ok' : 'icon-yijianfankui-d'"></view> -->
            </view>

            <!-- 订阅消息 -->
            <view class="subscribe" v-if="item.touchType === 6">
                <view class="subscribe-title">申请授权</view>
                <view class="subscribe-desc">
                    提醒您：为了让您更及时的接收消息，请您开通一下通知服务：
                </view>
                <view style="margin: 24upx 0;">
                  <view v-if="env.isOpenMsgSubscribe" class="subscribe-steps" :class="subscribeStep === 1 ? 'active' : ''">
                    <text class="subscribe-steps-num">1</text>
                    <text class="subscribe-steps-desc">接收小程序订阅消息</text>
                  </view>
                  <view
                    v-if="env.isOpenAccountSubscribe"
                    class="subscribe-steps"
                    :class="(
                      (!env.isOpenMsgSubscribe && subscribeStep === 1) ||
                      (env.isOpenMsgSubscribe && subscribeStep === 2)
                    ) ? 'active' : ''">
                    <text class="subscribe-steps-num">{{ env.isOpenMsgSubscribe ? 2 : 1 }}</text>
                    <text class="subscribe-steps-desc">接收公众号订阅消息</text>
                  </view>
                </view>
                <button class="btn" type="primary" size="mini" :disabled="subscribeStep !== 1" @click="subscribeMsg">{{ subscribeStep === 1 ? '立即开通' : '已开通' }}</button>
            </view>
        </view>

        <view class="order-end" v-if="orderDetail.consultStatus === 3">
          <view class="order-end-time">{{ orderDetail.endTime | getTimeStringAutoShort2 }}</view>
          <view style="text-align: center;"><view class="order-end-tips">服务已结束</view></view>
          <view class="order-end-evaluate">
            <view class="order-end-evaluate-title">请您对本次服务进行评价</view>
            <view class="order-end-evaluate-desc">真实有效的评价是我们提升服务的最大动力</view>
            <view style="width: 100%; margin-top: 32upx;">
              <evaluate
                :form="orderEvaluate"
                :disabled="orderDisable"
                @confirm="confirmEvaluate"
              />
            </view>
          </view>

          <text>您的咨询服务已完成，如需继续咨询，可点击下方按钮继续咨询</text>
          <br/>
          <button class="btn" type="primary" size="mini" @tap="handleConsult">继续咨询</button>
        </view>
    </view>
</template>

<script>
import { mapState } from 'vuex'
import TimeUtils from '@/common/util/websocket/timeUtils.js'
import evaluate from '@/modules/business/chat/components/evaluate.vue'
import env from '@/config/env/index'
export default {
  components: {
    evaluate
  },
  data() {
    return {
      env,
      // 为了兼容小程序可以直接在视图（标签）直接使用到全局挂载变量，所以需要页面变量data上赋值
      $constant: this.$constant,
      $common: this.$common,
      $accurateConversion: this.$accurateConversion,
      file_ctx: this.file_ctx,
      $static_ctx: this.$static_ctx,
      $timePlugin: this.$timePlugin,
      regForm: {
        platformNum: 0,
        campusNum: 0
      },
      // chat
      formData: {
        content: '',
        limit: 20,
        index: 1
      },
      messageList: [],
      loading: true, //标识是否正在获取数据
      imgHeight: '1000px',
      mpInputMargin: false, //适配微信小程序 底部输入框高度被顶起的问题
      chatType: "voice",  // 图标类型 'voice'语音 'keyboard'键盘
      PointY: 0, //坐标位置
      // chatItem: null,
      defaultAvatar: this.file_ctx + 'static/image/system/avatar/icon-default-avatar.png',
      chatItem: {},
      orderId: '',
      orderDetail: {},
      subscribeStep: 1
    }
  },
  mounted() {
    const { id } = this.$Route.query
    this.orderId = id
    this.joinData()
    this.getOrderDetail()
    if (env.isOpenAccountSubscribe) {
      this.checkAccountSubscribeStatus().then((res) => {
        if (!res) return
        this.subscribeStep = 2
      })
    }

  },
  computed: {
    ...mapState('user', {
      codeUserInfo: state => state.codeUserInfo, // 当前登录用户信息
      curSelectUserInfo: state => state.curSelectUserInfo, // 当前选中的用户信息
      curSelectUserInfoId: state => state.curSelectUserInfoId, // 当前选中的档案的ID
    }),
    ...mapState('chat', {
      nodereplyconfig: state => state.nodereplyconfig,
    }),
    // 小程序订阅消息节点配置
    nodeConfig() {
        return this.nodereplyconfig.find(item => item.pushType === 6)
    },
    // 订单评价
    orderEvaluate () {
      const { seReplyQuality = '', seServiceAttitude = '', seReplySpeed = '', seEvaluate = '' } = this.orderDetail || {}
      return [
        { key: 'seReplyQuality', value: seReplyQuality, label: '回复质量', type: 'rate' },
        { key: 'seServiceAttitude', value: seServiceAttitude, label: '服务态度', type: 'rate' },
        { key: 'seReplySpeed', value: seReplySpeed, label: '回复速度', type: 'rate' },
        { key: 'seEvaluate', value: seEvaluate, label: '评价文本', placeholder: '🖊谈谈您对本次就诊的印象吧~', type: 'text', config: {label: '', style: {width: '100%'}} },
      ]
    },
    // 订单是否已结束
    orderDisable () {
      if (this.$validate.isNull(this.orderDetail) || this.orderDetail.seReplyQuality || this.orderDetail.seServiceAttitude || this.orderDetail.seReplySpeed || this.orderDetail.seEvaluate) {
        return true
      } else {
        return false
      }
    }
  },
  watch: {
    orderDetail: {
      handler () {
        if (!env.isOpenMsgSubscribe) return
        if (!this.$validate.isNull(this.orderDetail) && this.orderDetail.subscribeStatus === 1) {
          this.subscribeStep = 2
        } else {
          this.subscribeStep = 1
        }
      },
      deep: true
    }
  },
  methods: {
    confirmEvaluate (list) {
      let that = this
      const { ORDER_EVALUATE_CMD, ORDER_GUIDE } = that.$constant.chat
      let param = {}
      list.forEach(item => {
        param[item.key] = item.value
      })
      // 用户评价节点配置
      // const nodereplyconfig = this.$common.getKeyVal('chat', 'nodereplyconfig').find(item => item.pushType === 8)
      // let chatDto =  {
      //     cmd: ORDER_GUIDE,
      //     data: {
      //         orderId: this.orderDetail.id,
      //         userId: this.orderDetail.initiatorUserId,
      //         chatUserId: this.orderDetail.receiveUserId,
      //         nodeConfigId: nodereplyconfig.id
      //     }
      // }
      // that.$ext.webSocket.webSocketSend(ORDER_GUIDE, chatDto)

      const data =  {
        cmd: ORDER_EVALUATE_CMD,
        data: {
          userId: this.orderDetail.initiatorUserId,
          orderId: this.orderDetail.id,
          ...param
        }

      }
      that.$ext.webSocket.webSocketSend(ORDER_EVALUATE_CMD, data)
      this.orderDetail = {
        ...this.orderDetail,
        ...param
      }
    },
    /**
     * 检查用户公众号订阅状态
     * @return Promise<boolean> 是否已经订阅
     */
    async checkAccountSubscribeStatus() {
      const unionid = await this.$ext.wechat.getUnionId()
      const res = await this.$api.common.accountattentionSubscribeOrNot({
        unionid,
        wxId: 'wx0918ff821e41f5c4'
      })
      if (!this.$validate.isNull(res.data)) return Promise.resolve(true)
      return Promise.resolve(false)
    },
    /**
     * 公众号订阅
     */
    accountSubscribe() {
      return new Promise(async (resolve, reject) => {
        this.$uniPlugin.modal('您还没关注消息助手，请前往开启', '', {
          showCancel: true, // 是否显示取消按钮，默认为 true
          cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
          cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
          confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
          confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
          fn: async (n) => {
            if (n) {
              this.subscribeStep = 2
              this.navtoGo('WebHtmlView', { src: env.subscribeUrl, title: '消息订阅' })
              resolve()
            } else {
              reject()
            }
          }
        })
      })
    },
    /**
     * 消息订阅
     */
    msgSubscribe() {
      return new Promise(async (resolve, reject) => {
        await this.$uniPlugin.subscribeMessage()
        uni.requestSubscribeMessage({
          tmplIds: ['s26obqeypTtrQZHJVoe8DU7EiD_IPYGAUC2-zD6GzzM'],
          success: (res) => {
            resolve()
            this.subscribeStep = 2
          },
          fail: (err) => {
            resolve()
            console.log('调用失败-----------------', err)
          }
        })
      })
    },
    async subscribeMsg () {
      if (env.isOpenMsgSubscribe) {
        await this.msgSubscribe()
        }
      if (env.isOpenAccountSubscribe) {
        await this.accountSubscribe()
      }

    },
    async getOrderDetail () {
      const res = await this.$api.chat.orderGetById({ id: this.orderId })
      this.orderDetail = res.data
    },
    clickCard (e) {
      const { text, urlPath, minAppid } = e
      switch (e.msgType) {
        // H5卡片
        case 5:
          this.$navto.push('WebHtmlView', { src: urlPath, title: text })
          break
        // 小程序卡片
        case 6:
          console.log(minAppid, e)
          uni.navigateToMiniProgram({
            appId: minAppid,
            path: '', // 打开的页面路径
            extraData: {}, // 需要传递的参数
            success: res => {

            },
            fail: err => {
              console.log('打开失败------', err)
            }

          })
          break
        default:
      }
    },
    uploadFile () {

    },
    // 继续咨询
    handleConsult () {
      const that = this
      const modalConfig = {
        showCancel: true, // 是否显示取消按钮，默认为 true
        cancelText: '取消', //  取消按钮的文字，默认为"取消"，最多 4 个字符
        cancelColor: '#000000', //  取消按钮的文字颜色，默认为"#000000"(H5、微信小程序、百度小程序)
        confirmText: '确定', //  确定按钮的文字，默认为"确定"，最多 4 个字符
        confirmColor: '#15c398', //  确定按钮的文字颜色，H5平台默认为"#007aff"，微信小程序平台默认为"#3CC51F"，百度小程序平台默认为"#3c76ff"（H5、微信小程序、百度小程序）
        fn: async (n) => {
          if (n) {
            const tenantId = that.$common.getKeyVal('user', 'curSelectStoreId', true) || env.tenantId
            that.$uniPlugin.loading('加载中', true)
            that.$api.order.orderEndInitiatorUserCheck({ userId: this.codeUserInfo.id, tenantId, chatUserId: that.orderDetail.receiveUserId }).then(res => {
              that.$uniPlugin.hideLoading()
              that.$navto.replace('Chat', res.data)
            }).catch(() => {
              that.$uniPlugin.hideLoading()
            })
          }
        }
      }
      that.$uniPlugin.modal('','是否确认继续咨询？',modalConfig)
    },
    // 查看图片（多张）
    viewImgs (e, eIndex) {
      let current = 0
      const imgs = this.messageList.filter(item => {
        return item.msgType == 2
      }).map((item, index) => {
        if (item.id === e.id) {
          current = index
        }
        return this.file_ctx + item.content
      })
      uni.previewImage({
        current,
        urls: imgs,
        // #ifndef MP-WEIXIN
        indicator: 'number'
        // #endif
      })
    },
    //查看大图
    viewImg(imgList) {
      uni.previewImage({
        urls: imgList,
        // #ifndef MP-WEIXIN
        indicator: 'number'
        // #endif
      });
    },
    navtoGo(url, obj) {
      const parameter = obj || {}
      this.$navto.push(url, parameter)
    },
    //拼接消息 处理滚动
    async joinData() {
      const that = this
      if (!this.loading) {
        //如果没有获取数据 即loading为false时，return 避免用户重复上拉触发加载
        return;
      }
      // that.loading = false;
      this.loading = false

      const data = await this.getMessageData();
      //获取节点信息
      const {index} = this.formData;
      const messageList = [...data, ...that.messageList];
      // console.log("messageList:",that.messageList)
      if (messageList.length == 0) {
        that.loading = false;
        return
      }
      const sel = `#msg-${index > 1 ? that.messageList[0].hasBeenSentId : data[data.length - 1].hasBeenSentId}`;
      that.messageList = messageList
      //填充数据后，视图会自动滚动到最上面一层然后瞬间再跳回bindScroll的指定位置 ---体验不是很好，后期优化
      that.$nextTick(() => {
        that.bindScroll(sel);
        if (data.length == that.formData.limit) {
          that.formData.index++;
          setTimeout(() => {
            that.loading = true;
          }, 200);
        } else {
          setTimeout(() => {
            // that.$uniPlugin.toast('没有更多数据啦！')
            that.loading = false;
          }, 200);
        }
      });

    },
    //处理滚动
    bindScroll(sel, duration = 0) {
      const query = uni.createSelectorQuery().in(this);
      query
        .select(sel)
        .boundingClientRect(data => {
          uni.pageScrollTo({
            scrollTop: data && data.top,
            duration
          });
        })
        .exec();
    },
    //获取消息
    getMessageData() {
      const that = this
      let getData = async () => {
        let arr = [];
        const param = {
            orderId: this.orderId,
            lastMsgId: this.$validate.isNull(this.messageList) ? '' : this.messageList[0].id,
            pageSize: this.formData.limit
        }
        const res = await that.$api.chat.singlemsgQueryPage(param)
        for (let i = 0; i < res.data.length; i++) {
          let item = res.data[i]
          arr.push(that.etlMsgItem(item, 1));
        }
        return Promise.resolve(arr);
      };
      return new Promise(async (resolve, reject) => {
        const data = await getData();
        resolve(data)
      });
    },
    /**
     * 转化msg的数据魔板
     * @param item
     * @param msgCloudStatus
     * @returns {{readStatus: (number|*), msgCloudStatus, createTime: *, fromUserId: (default.methods.chatItem.userId|*), hasBeenSentId, isItMe: boolean, fromUserHeadImg: *, contentType: number, content: default.methods.formData.content}}
     */
    etlMsgItem(item, msgCloudStatus) {
        const { centerUserId = '' } = this.curSelectUserInfo || {}
        let isItMe = item.fromUserId + '' == centerUserId + '' //true此条信息是我发送的 false别人发送的

        let content = item.msgContent
        try {
            content = JSON.parse(content)
        } catch {

        }

        let result = {
            ...item,
            hasBeenSentId: item.id, //已发送过去消息的id
            content: this.$validate.judgeTypeOf(content) === 'Object' ? content.text : content,
            text: this.$validate.judgeTypeOf(content) === 'Object' ? content.text : content,
            attach: (this.$validate.judgeTypeOf(content) === 'Object' && item.msgType == 8) ? content.attach : '',
            desc: (this.$validate.judgeTypeOf(content) === 'Object' && item.msgType == 8) ? JSON.parse(content.desc) : '',
            fromUserHeadImg: item.fromUserHeadPath, //用户头像
            fromUserId: item.fromUserId,
            isItMe: isItMe,
            createTime: item.createTime,
            // contentType: 1, // 1文字文本 2语音
            msgCloudStatus: msgCloudStatus, //消息发送结果状态，1是正常，2是发送中，待回调，3是等待回调失败
            readStatus: item.readStatus,
        }
        return result
    },
    //用户触摸屏幕的时候隐藏键盘
    touchstart() {
      uni.hideKeyboard();
    },
    isShowTime(index,protoMessages){
      if (index === 0) return true
       var msgTime = protoMessages[index].updateTime;
       if(index > 0){
           var preProtoMessage = protoMessages[index - 1];
           var preMsgTime = preProtoMessage.updateTime;
           if(msgTime - preMsgTime > ( 5 * 60 * 1000)){
               return true;
           }
       }
       return false;
    },
  },
  filters: {
    getTimeStringAutoShort2(timestamp){
        try {
            timestamp = Number(timestamp) ? Number(timestamp) : timestamp
        } catch (err) {

        }
        return TimeUtils.getTimeStringAutoShort2(new Date(timestamp).getTime(),true);
    }
  }
}
</script>
<style lang="scss" scoped>
@import '../../chat/components/message.scss';
</style>
