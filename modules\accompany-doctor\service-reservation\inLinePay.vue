<template>
  <view class="guidanceBox">
    <view class="headerTap">
      <view class="cencelTitle">
        <image class="cencelTitleIcon" :src="paid" mode="aspectFill"></image>
        {{ accompanybookOne.pay ? "支付成功，等待服务" : "待支付" }}
        <!-- <text v-if="!accompanybookOne.pay && accompanybookOne.expiredTime" class="surplus">剩余</text>
        <text v-if="!accompanybookOne.pay && accompanybookOne.expiredTime" class="serplusTime">{{countDownTime}}</text> -->
      </view>
    </view>
    <view class="tapClue" v-if="currentProviderName">
      本次服务由（{{ currentProviderName }}）为您提供
    </view>
    <!-- 锁单文案 -->
    <view class="guidanceTwoTitle" v-if="isLocking && !accompanybookOne.pay">{{lockText}}</view>
    <!-- 取消保障文案 -->
    <view class="insureSign" v-if="insuredInfo.pay && isSelectInsurance && !isRefundInsure">
        <view class="">门诊无忧保障中，就诊更安心</view>
        <view class="surrender" @click="clearEnsure">取消保障</view>
    </view>
    <!-- 已取消保障文案 -->
    <view class="insureSign" v-if="isRefundInsure">
        <view class="">已取消保障，已退款￥{{insuredInfo.price / 100}}</view>
    </view>
    <!-- S 服务名称 -->
    <view class="guidanceCard">
      <view class="serverTitle">
        <image
          class="serverIcon"
          :src="file_ctx + accompanybookOne.serviceListImg"
          mode=""
        ></image>
        <view class="serviceName">
          <view class="serverNameT">{{ accompanybookOne.serviceName }}</view>
          <view class="serverTime"
            ><text class="tabTitle">就诊时间:</text
            >{{ timestampToDateTime(accompanybookOne.startTime) }}~{{
              timestampToDateTime(accompanybookOne.endTime, true)
            }}</view
          >
          <view class="serverTime"
            ><text class="tabTitle">就诊医院:</text
            >{{ accompanybookOne.hospitalName }}</view
          >
          <view class="payNums">
            <text class="paySign">￥</text>
            {{ accompanybookOne.payPrice / 100 }}
          </view>
        </view>
      </view>
    </view>
    <!-- E 服务名称 -->

    <!-- S 保险信息 -->
    <view class="Insurance" :class="{ selectInsurance: isSelectInsurance }" v-if="provinceValue && ((provinceValue.insureButton === 1) || (provinceValue.insureButton === 2 && underInfo && underInfo.certfNo)) && accompanybookOne && !accompanybookOne.pay && accompanybookOne.startTime && showInsuranceContent(accompanybookOne.startTime) && !isAgeOver65">
      <!-- 白框区域 -->
      <view class="InsuranceInfo">
        <!-- 保险内容 -->
        <view class="InsuranceContent">
          <!-- logo -->
          <image class="Group" :src="isSelectInsurance ? Shield : shieldUnchecked" mode=""></image>
          <!-- 中间部位 -->
          <view class="InsuranceInfoMiddle">
            <view class="InsuranceContentTitle">
              <view class="title">门诊无忧服务</view>
              <view class="miniTitle" @click="showInsureInfo">
                其他产品
                <image class="right_arrow" :src="right_arrow" mode=""></image>
              </view>
            </view>
            <view class="InsurancePrompt">最高{{insuranceNum * 2}}万元意外保障</view>
            <view class="pay">
              <text class="dollarSign">￥</text>
              <text class="payNum">{{insuranceNum}}</text>
              <text class="everyone">/</text>
              <text class="man">人</text>
            </view>
          </view>
          <!-- 选择按钮 -->
        <view class="selectBox" @click="selectInsurance">
          <image
            class="selectorButton"
            :src="isSelectInsurance ? iconPostSucess : Ellipse"
          >
          </image>
        </view>
        </view>
        <view class="describe">
            <image class="Check" :src="isSelectInsurance ? CheckGreen : Check" mode=""></image>
            就医导诊
            <image class="Check" :src="isSelectInsurance ? CheckGreen : Check" mode=""></image>
            就医提醒
            <image class="Check" :src="isSelectInsurance ? CheckGreen : Check" mode=""></image>
            法律咨询
        </view>
      </view>
      <view class="notice" :class="{shake:isShake}">
        <image
          class="selectorButton"
          @click="setIsNotice"
          :src="isNotice ? iconPostSucess : Ellipse"
        >
        </image>
        我已阅读并同意
        <text class="specification">
        <text @click="showInsureInfo">【服务须知】</text>
        <text @click="$navto.push('instruction',{insuranceNum:insuranceNum})">【服务细则】</text>
        <text @click="$navto.push('instruction',{showText:true})">【信息安全】</text>
        </text>
      </view>
    </view>
    <!-- E 保险信息 -->

    <!-- S 订单信息 -->
    <view class="guidanceCard">
      <view class="orderInfo" v-if="!accompanybookOne.pay">
        <view class="orderTitle">订单信息</view>
        <view class="orderValue">
          <text class="orderIdTitle">订单号</text>
          <view class="">
            {{ accompanybookOne.id }}
            <text class="copy" @click="handleCopyOrder">复制</text>
          </view>
        </view>
        <view class="orderValue">
          <text class="timeTitle">创建时间</text>
          {{ timestampToDateTime(accompanybookOne.createTime) }}
        </view>
      </view>
      <view class="orderMap" v-else>
        <view class="orderInfo">
          <view class="orderTitle">订单信息</view>
          <view class="orderValue">
            <text class="orderIdTitle">订单号</text>
            <view class="">
              {{ accompanybookOne.id }}
              <text class="copy" @click="handleCopyOrder">复制</text>
            </view>
          </view>
          <view class="orderValue">
            <text class="timeTitle">创建时间</text>
            {{ timestampToDateTime(accompanybookOne.createTime) }}
          </view>
        </view>
        <view class="serverNameT">
          <view class="serverNameTitle">总价格</view>
          <text class="payPrice" v-if="insuredInfo.pay && !insuredInfo.refundInsure">￥{{accompanybookOne.payPrice / 100 + serverOptions.productCodeMap.filter(e=>e.productCode === insuredInfo.productCode)[0].insuranceNum}}</text>
          <text class="payPrice" v-else>￥{{accompanybookOne.payPrice / 100 }}</text>
        </view>
        <view class="orderItem">
        <text class="orderTitle">服务费</text>
        ￥{{accompanybookOne.payPrice / 100}}
        </view>
        <view class="orderItem" v-if="insuredInfo.pay && !insuredInfo.refundInsure">
          <text class="orderTitle">门诊无忧服务费用</text>
          ￥{{serverOptions.productCodeMap.filter(e=>e.productCode === insuredInfo.productCode)[0].insuranceNum}}
        </view>
        <view class="orderItem">
          <text class="orderTitle">支付方式</text>
          {{getOrderType()}}
        </view>
        <view class="orderItem">
          <text  class="orderTitle">支付时间</text>
          {{timestampToDateTime(accompanybookOne.payTime)}}
        </view>

      </view>
    </view>
    <!-- E 订单信息 -->
    <!-- 具体明细 -->
    <view :class="{showDetailsBox:showDetailsBox}" class="DetailsBox" :style="getShowBtnBottom" >
      <!-- 标题 -->
      <view class="confirmTitle">
        费用明细
        <image class="iconClose" @click="showDetailsBox = false" :src="iconClose" mode=""></image>
      </view>
      <view class="DetailsContent">
        <view class="DetailsLine">
          <view class="lineTitle">总价格</view>
          <view class="">
            <text class="lineSign">￥</text>
            <text class="totalPrice">{{ getPays }}</text>
          </view>
        </view>
        <view class="DetailsLine">
          <text>服务费</text>
          <text>￥{{ accompanybookOne.payPrice / 100 }}</text>
        </view>
        <view class="DetailsLine" v-if="getInsuranceNum">
          <text>门诊无忧服务费用</text>
          <text>￥{{getInsuranceNum}}</text>
        </view>
      </view>
    </view>
    <!-- 按钮 -->
    <view class="buttonMap" v-if="!accompanybookOne.pay">
      <agreement ref="agreementRef" class="agreement" v-model="isAgreed" :provinceValue="provinceValue" @onLoadDocument="onLoadDocument"></agreement>
      <view class="Detail">
        <view class="pays">￥{{ getPays }}</view>
        <view class="showBtn" :class="{showBtnUpDown:showDetailsBox}" @click="showDetailsBoxFn">
          明细
          <image class="right_arrow" :src="right_arrow" mode=""></image>
        </view>
        <view class="line"></view>
      </view>
      <view class="clearBtn" @click="clearOrder">取消订单</view>
      <view class="inLinePayBtn" @click="inLinePay">{{ PayText }}</view>
    </view>
    <!-- 支付后按钮 -->
    <view class="bottom" v-else>
      <view class="bottomButtonMap" v-if="insuredInfo.pay && !insuredInfo.refundInsure">
        <view class="buttonItem" @click="getEpolicyUrl">门诊无忧服务单</view>
        <view class="buttonItem" @click="gotoInsuranceList">门诊无忧服务开票</view>
      </view>
      <view class="bottomClearBtn" @click="clearOrder">取消订单</view>
    </view>
    <selectComboPop
      @change="changeComboPop"
      @selectCombo="selectCombo"
      :comboDataMap="comboDataMap"
      :openFlag="showComboMap"
    ></selectComboPop>
    <view v-if="showDetailsBox" @click="showDetailsBox = false" class="mask"></view>
  </view>
</template>

<script>
import serverOptions from "@/config/env/options";
import common from "@/common/util/main";
import selectComboPop from "../components/selectComboPop";
import agreement from "../components/agreement.vue";

function debounce(func, delay) {
  let timer;
  return (...args)=> {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
}
export default {
  components: {
    selectComboPop,
    agreement
  },
  props: {
    accompanybookOne: {
      type: Object,
    },
    underInfo: {
      type: Object,
      default:{}
    },
    insuredInfo: {
      type: Object,
      default:{}
    },
  },
  computed: {
    getShowBtnBottom(){
      if(!this.showDetailsBox || !this.buttonMapHeight) return ''
      return `bottom:${this.buttonMapHeight}px`
    },
    //判断是否退单 返回true 则是退单 返回false 则是未退单
    isRefundInsure(){
      return this.insuredInfo.refundInsure === 1;
    },
    getPays() {
      return this.isSelectInsurance ? this.accompanybookOne.payPrice / 100 + this.insuranceNum : this.accompanybookOne.payPrice / 100;
    },
    getInsuranceNum(){
      if(!this.isSelectInsurance) return 0
      return this.insuranceNum
    },
    async getAccompanybookOne() {
      // 设置支付按钮文字内容
      await this.setPayText();
      let {
        data: {
          records: [serverCurrent],
        },
      } = await this.$api.accompanyDoctor.getAccompanyservicePage({
        condition: { serviceId: this.accompanybookOne.serviceId },
      });
      this.serverCurrent = serverCurrent;
      return this.accompanybookOne;
    },
    // 判断年龄是否超过65岁
    isAgeOver65() {
      // 如果没有就诊人信息则返回false
      if (!this.underInfo) return false;

      let isOverFromIdCard = false;
      let isOverFromAge = false;

      // 从身份证号判断年龄
      if (this.underInfo.certfNo && this.underInfo.certfNo.length === 18) {
        const birthYear = parseInt(this.underInfo.certfNo.substring(6, 10));
        const currentYear = new Date().getFullYear();
        const ageFromIdCard = currentYear - birthYear;
        isOverFromIdCard = ageFromIdCard > 65;
      }

      // 从年龄字段判断
      if (this.insuredInfo.age) {
        isOverFromAge = parseInt(this.insuredInfo.age) > 65;
      }
      // 只要有一个条件满足就返回true
      return isOverFromIdCard || isOverFromAge;
    },
  },
  watch:{
    insuredInfo:{
      handler(newVal,oldVal) {
        if(this.insuredInfo.productCode){
          // 根据产品编码查找对应的保险金额
          const matchedProduct = this.serverOptions.productCodeMap.find(e => e.productCode === newVal.productCode);
          if(matchedProduct) {
            this.insuranceNum = matchedProduct.insuranceNum;
            this.isSelectInsurance = true;
            this.isNotice = true;
          } else {
            console.log('未找到匹配的保险产品:', newVal.productCode);
          }
        }
      },
      immediate: true,
      deep:true
    },
    accompanybookOne:{
      async handler(newVal,oldVal) {
        console.log('accompanybookOneWatch', newVal);
        if(!this.accompanybookOne.id) return
        let data = await this.$ext.user.isPayedOrder(this.accompanybookOne.id,1,this.accompanybookOne.providerId);
        // 此单已被锁定
        if(data){
          this.isLocking = true;
        }

        // 如果有过期时间且未支付，启动倒计时
        if (this.accompanybookOne.expiredTime && !this.accompanybookOne.pay) {
          this.startCountDown();
        } else if (!this.accompanybookOne.pay && this.accompanybookOne.createTime) {
          // 如果没有过期时间但有创建时间，计算默认过期时间（创建后24小时）
          this.calculateDefaultExpireTime();
        }
      },
      immediate: true,
      deep:true
    },
  },
  data() {
    return {
      buttonMapHeight:0,
      isAgreed:true,
      lockText:'当前订单已锁单，无法变更订单金额',
      file_ctx: this.file_ctx,
      paid: this.$static_ctx + "image/business/hulu-v2/paid.png",
      round: this.$static_ctx + "image/business/hulu-v2/round.png",
      right_arrow: this.$static_ctx + "image/business/hulu-v2/right-arrow.png",
      Ellipse: this.$static_ctx + "image/business/hulu-v2/Ellipse.png",
      iconPostSucess:
        this.$static_ctx + "image/business/hulu-v2/icon-post-sucess.png",
      Check: this.$static_ctx + "image/business/hulu-v2/Check.png",
      CheckGreen: this.$static_ctx + "image/business/hulu-v2/CheckGreen.png",
      Shield: this.$static_ctx + "image/business/hulu-v2/Shield.png",
      shieldUnchecked: this.$static_ctx + "image/business/hulu-v2/shield-unchecked.png",
      entering: this.$static_ctx + "image/business/hulu-v2/entering.png",
      iconClose: this.$static_ctx + "image/business/hulu-v2/icon-close2.png",
      currentServer: {},
      showComboMap: false,
      comboDataMap: [],
      serverCurrent: {},
      PayText: "",
      payCurrentProviderId:"",
      currentProviderName: "",
      gotoFlag: false,
      isSelectInsurance: false,
      isNotice:false,
      showDetailsBox:false,
      directPayment:false,
      insuranceNum:5,
      isShake:false,
      isLocking:false,
      gotoAppId:'',
      serverOptions,
      clearEnsure:debounce(()=>this.$emit('ConfirmPurchaseInsurance','surrender'),300),
      provinceValue:{},
      countDownTime: '00:00:00', // 倒计时显示
      countDownTimer: null, // 倒计时定时器
    };
  },
  async mounted() {
    console.log('this.serverOptions',this.serverOptions);
    let provinceValue = (await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:serverOptions.providerId})).data;
    this.provinceValue = provinceValue;

    // 如果有过期时间，启动倒计时
    if (this.accompanybookOne && this.accompanybookOne.expiredTime && !this.accompanybookOne.pay) {
      this.startCountDown();
    }
  },
  beforeDestroy() {
    // 清除定时器
    if (this.countDownTimer) {
      clearInterval(this.countDownTimer);
    }
  },
  methods: {
    onLoadDocument(urlList){
      // 读取buttonMap盒子的高度
      this.$nextTick(()=>{
        const query = uni.createSelectorQuery().in(this);
        query.select('.buttonMap').boundingClientRect();
        query.exec((res) => {
          console.log('buttonMap高度',res);
          this.buttonMapHeight = res[0].height;
        })
      })
    },
    // 获取当前订单支付的方法
    getOrderType(){
      let {comboPay,orderType,payType} = this.accompanybookOne;
      if(comboPay) return '套餐支付';
      if(orderType) return orderType === 1 ? '线下支付' : '联合订单支付';
      return ['','微信支付','支付宝支付'][payType];
    },
    // 启动倒计时
    startCountDown() {
      // console.log('启动倒计时，过期时间:', this.accompanybookOne.expiredTime);
      if (this.countDownTimer) {
        clearInterval(this.countDownTimer);
      }

      // 更新倒计时
      this.updateCountDown();

      // 每秒更新一次倒计时
      this.countDownTimer = setInterval(() => {
        this.updateCountDown();
      }, 1000);
    },

    // 更新倒计时显示
    updateCountDown() {
      if (!this.accompanybookOne.expiredTime) {
        this.countDownTime = '00:00:00';
        return;
      }

      const now = new Date().getTime();
      const expiredTime = new Date(this.accompanybookOne.expiredTime).getTime();
      let diff = Math.max(0, expiredTime - now) / 1000; // 转换为秒

      if (diff <= 0) {
        // 时间到，清除定时器
        clearInterval(this.countDownTimer);
        this.countDownTime = '00:00:00';
        return;
      }

      // 计算天、时、分、秒
      const days = Math.floor(diff / 86400); // 一天有86400秒
      diff -= days * 86400;
      const hours = Math.floor(diff / 3600);
      diff -= hours * 3600;
      const minutes = Math.floor(diff / 60);
      diff -= minutes * 60;
      const seconds = Math.floor(diff);

      // 根据剩余时间长短选择不同的显示格式
      if (days > 0) {
        // 如果有天数，则显示为 "xx天xx小时"
        this.countDownTime = days + '天' + hours + '小时';
      } else if (hours > 0) {
        // 如果只有小时，则显示为 "xx:xx:xx"
        this.countDownTime =
          (hours < 10 ? '0' + hours : hours) + ':' +
          (minutes < 10 ? '0' + minutes : minutes) + ':' +
          (seconds < 10 ? '0' + seconds : seconds);
      } else {
        // 如果只有分钟和秒，则显示为 "xx:xx"
        this.countDownTime =
          (minutes < 10 ? '0' + minutes : minutes) + ':' +
          (seconds < 10 ? '0' + seconds : seconds);
      }
    },
    showInsuranceContent(timestamp){
      let currentTime = new Date().getTime();
      const date = new Date(timestamp);
      date.setHours(0, 0, 0, 0);
      let endTime = date.getTime();
      if(currentTime < endTime){
        return true
      }
    },
    gotoInsuranceList(){
      // 判断是否已退单如果已经退单则不进行跳转
      if(this.isRefundInsure) return uni.showToast({title:'该订单已取消保障',icon:'none'})
      this.$navto.push('insuranceList',{accompanyId:this.accompanybookOne.id})
    },
    async getEpolicyUrl(){
      // 判断是否已退单如果已经退单则不进行下载
      if(this.isRefundInsure) return uni.showToast({title:'该订单已取消保障',icon:'none'})
        uni.showLoading({title:'加载中',mask:true})
        let data = await this.$api.accompanyDoctor.accompanyinsureEpolicyUrl({accompanyId:this.accompanybookOne.id})
        if(!data.msg){
          uni.hideLoading()
          return uni.showToast({ title: "暂无门诊无忧服务单", icon: "none" });
        }
        wx.downloadFile({ //将文档下载到本地
                url: data.msg,//pdf链接
                success(res) {
                  wx.openDocument({ //打开文档
                    filePath: res.tempFilePath,//本地文档路径
                    fileType: "pdf",//文档类型
                    showMenu: true,
                    success: function (res) {
                      wx.showToast({
                        title: '打开文档成功',
                      })
                      uni.hideLoading()
                    },
                    fail: function (res) {
                      wx.showToast({
                        title: '打开文档失败',
                      })
                      uni.hideLoading()
                    },
                  })
                },
              })
      },
    updateInsurance(insuranceNum){
      this.insuranceNum = insuranceNum;
    },
    showDetailsBoxFn(){
      this.showDetailsBox = !this.showDetailsBox;
    },
    selectInsurance() {
      if(this.isLocking) return uni.showToast({title:this.lockText,icon:'none'})
      this.isSelectInsurance = !this.isSelectInsurance;
      this.isNotice = this.isSelectInsurance;
      let productCode = serverOptions.productCodeMap.filter(e=>e.insuranceNum === this.insuranceNum)[0].productCode;
      // 如果当前取消了保险 则将productCode设置为空
      if(!this.isSelectInsurance) productCode = '';
      this.$emit('setProductCode',{productCode})
    },
    setIsNotice(){
      if(this.isLocking) return uni.showToast({title:this.lockText,icon:'none'})
      this.isNotice = !this.isNotice;
      this.$emit('changesetIsNotice');
    },
    showInsureInfo(){
      // 传递当前保险金额
      this.$emit('ConfirmPurchaseInsurance','insureInfo',this.isLocking,{insuranceNum:Number(this.insuranceNum)});
    },
    async setPayText() {
      try {
        let currentProviderId = this.accompanybookOne.providerId;
        console.log('serverOptions.appId',serverOptions.appId,this.accompanybookOne.appid);
        // 当前处于云陪诊模块 如果出现服务商id一致 但是appid不一致的情况 则说明是云陪诊独立模块的订单 那这种情况下直接比对原始appid（小葫芦自己的id）即可
        if(this.accompanybookOne.appid && this.accompanybookOne.appid !== serverOptions.getoptions().appId){
          this.PayText = "前往支付";
          this.currentProviderName = this.accompanybookOne.providerName;
          this.gotoFlag = true;
          this.gotoAppId = this.accompanybookOne.appid;
          return
        }
        // 判断当前这条订单的真实服务商是否是当前平台
        if (serverOptions.providerId !== currentProviderId) {
          this.currentProviderName = this.accompanybookOne.providerName;
          // 查询当前订单对应的服务商信息
          let {data} = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id:this.accompanybookOne.providerId});
          if(!data) return uni.showToast({ title: '当前服务商信息有误', icon: "none" });
          // 若当前订单服务商开启了独立小程序则直接让用户跳转前往该独立小程序
          // storeButton 是否开启云门店 appidButton 是否开启独立小程序
          if(!data.storeButton && data.appidButton){
            this.PayText = "前往支付";
            this.gotoFlag = true;
            return;
          }
          // 若当前订单服务商未开启独立小程序则认为是云服务商 可以直接进行支付 并修改当前支付的服务商id
          this.payCurrentProviderId = currentProviderId
        }
        this.PayText = `在线支付￥${this.getPays}`;
      } catch (error) {
        console.log('error',error);
        uni.showToast({ title: "加载失败" + error, icon: "none" });

      }
    },
    handleCopyOrder() {
      uni.setClipboardData({
        data: this.accompanybookOne.id,
        success: function () {
          uni.showToast({ title: "复制成功", icon: "success" });
        },
      });
    },
    clearOrder() {
      if (this.accompanybookOne.pay) {
        return this.$emit("clearOrder");
      }
      uni.showModal({
        title: "提示",
        content: "确定要取消订单吗？",
        success: async (res) => {
          if (res.confirm) {
            let {
              data: { records, total },
            } = await this.$api.accompanyDoctor.accompanybookCancel({
              id: this.accompanybookOne.id,
              cancelReason: "用户主动取消",
              refundAmount: this.accompanybookOne.payPrice || 0 // 全额退款，单位分
            });
            try {
              uni.navigateBack();
            } catch (error) {
              this.$navto.replaceAll('AccompanyHome')
            }
            setTimeout(function () {
              uni.showToast({
                title: "取消成功",
                icon: "none",
              });
            }, 10);
          }
        },
      });
    },
    changeComboPop(flag) {
      this.showComboMap = flag;
    },
    selectCombo(res) {
      console.log("res", res);
      this.$emit("comboPay", res);
    },
    setDirectPayment(flag){
      this.directPayment = flag;
      this.showDetailsBox = false;
    },
    async inLinePay() {
      console.log('触发在线支付方法 - 开始', {
        isSelectInsurance: this.isSelectInsurance,
        insuranceNum: this.insuranceNum,
        getPays: this.getPays,
        accompanyPrice: this.accompanybookOne.payPrice / 100
      });
      const isAgreed = await this.$refs.agreementRef.checkAgreement();
      if (!isAgreed) return;
      if (this.gotoFlag) {
        let currentProviderId = this.accompanybookOne.providerId;
        let appId = serverOptions.optionsMap.filter(
          (e) => e.providerId === currentProviderId
        )[0]?.appId;
        appId = this.gotoAppId || appId;
        // 跳转到其他的小程序去进行支付
        uni.navigateToMiniProgram({
          appId,
          path:
            "modules/accompany-doctor/service-reservation/index?orderId=" +
            this.accompanybookOne.id,
          envVersion: "develop",
          success(res) {
            // 打开成功
            console.log("跳转成功", res);
          },
        });
        return;
      }
      // 判断当前服务商是否需要购买保险 如果无需购买则跳过所有保险相关业务
      if(!this.provinceValue.insureButton || (this.provinceValue.insureButton === 2 && !this.underInfo.certfNo) || this.isAgeOver65) {
        console.log('服务商不需要购买保险、insureButton为2且无身份证或年龄超过65岁，跳过保险业务');
        return this.noInsurePay();
      }
      // 如果此单已被锁定 则直接跳转支付
      if(this.isLocking){
        console.log('此单已被锁定，直接跳转支付');
        this.directPayment = false
        this.$emit("inLinePay",this.isSelectInsurance,this.payCurrentProviderId);
        return
      }
      // 如果当前购买保险的时间已经过了 则直接跳转支付
      if(!this.showInsuranceContent(this.accompanybookOne.startTime)){
        console.log('保险购买时间已过');
        this.directPayment = false
        this.$emit("inLinePay",this.isSelectInsurance,this.payCurrentProviderId);
        return
      }
      // 如果当前未选择购买保险 则弹出弹窗让用户去选择
      if(!this.isSelectInsurance){
        console.log('未选择保险，弹出风险提示');
        if(!this.directPayment){
          return this.$emit('ConfirmPurchaseInsurance','riskAlert');
        }
      }
      // 如果当前选择了购买保险 但是未填同意须知 则弹出弹窗让用户去填入
      if(this.isSelectInsurance && !this.isNotice){
          console.log('已选择保险但未同意须知');
          uni.showModal({
            title: '服务须知和产品服务细则',
            content: '我已阅读并同意【服务须知】和【产品服务细则】',
            success: (res)=> {
              if (res.confirm) {
                this.isNotice = true;
                this.inLinePay()
              } else if (res.cancel) {
                console.log('用户点击取消');
              }
            }
          });
          this.isShake = true;
          setTimeout(() => {
            this.isShake = false;
          },1000)
          return
      }
      // 判断当前是否购买了套餐
      // 勾选保险时不能使用套餐抵扣
      if(!this.isSelectInsurance && await this.hasCombo()) {
        console.log('已购买套餐，使用套餐支付');
        return;
      }

      console.log('准备发出支付请求', {
        isSelectInsurance: this.isSelectInsurance,
        productCode: serverOptions.productCodeMap.filter(e=>e.insuranceNum === this.insuranceNum)[0]?.productCode,
        insuranceNum: this.insuranceNum,
        finalPrice: this.getPays
      });

      this.directPayment = false
      this.$emit("inLinePay",this.isSelectInsurance,this.payCurrentProviderId);
    },
    // 无保险业务的支付
    async noInsurePay(){
      console.log('触发无保险业务',await this.hasCombo());

      if(await this.hasCombo()) return
      this.directPayment = false
      this.$emit("inLinePay",this.isSelectInsurance,this.payCurrentProviderId);

    },
    // 判断是否有套餐可以使用
    async hasCombo() {
      // 直接读取订单中的用户id
      let userId = this.accompanybookOne.userId;
      // 如果当前查询不到用户id 则直接进入支付流程
      if(!userId) return false
      // 获取用户套餐信息 如果有套餐 那么进入套餐支付程序 如果没有套餐 那么进入支付程序
      let { data } = await this.$api.accompanyDoctor.getAccompanycombouserQueryUserCombo({
        serviceId: this.accompanybookOne.serviceId,
        userId: userId,
      });
      if (data.length > 0) {
        this.comboDataMap = data;
        this.showComboMap = true;
        return true;
      }
      return false;
    },
    // 仅检测自身可枚举属性
    isObjectNotEmpty(obj) {
      return Object.keys(obj).length > 0
    },
    timestampToDateTime(timestamp, flag) {
      if (!timestamp) return "";
      // 创建一个新的Date对象，传入的时间戳是以毫秒为单位的
      var date = new Date(timestamp);
      // 获取年、月、日、时、分、秒
        var year = date.getFullYear();
      var month = ("0" + (date.getMonth() + 1)).slice(-2); // 月份是从0开始的，所以+1，并补零
      var day = ("0" + date.getDate()).slice(-2); // 获取天数，并补零
      var hour = ("0" + date.getHours()).slice(-2); // 获取小时，并补零
      var minute = ("0" + date.getMinutes()).slice(-2); // 获取分钟，并补零
        if (flag) return `${month}.${day} ${hour}:${minute}`;
      // 返回格式化的字符串
        return `${year}.${month}.${day} ${hour}:${minute}`;
    },
    // 计算默认的过期时间（创建时间 + 24小时）
    calculateDefaultExpireTime() {
      if (!this.accompanybookOne.createTime) return;

      // console.log('计算默认过期时间，创建时间:', this.accompanybookOne.createTime);
      const createTime = new Date(this.accompanybookOne.createTime).getTime();
      const expiredTime = new Date(createTime + 24 * 60 * 60 * 1000); // 创建时间 + 24小时

      // 设置过期时间
      this.accompanybookOne.expiredTime = expiredTime;
      // console.log('设置默认过期时间:', expiredTime);

      // 启动倒计时
      this.startCountDown();
    },
  },
};
</script>

<style lang="scss">
  .insureSign{
    width: 100%;
    height: 60rpx;
    background: #E0F4EF;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 1rpx solid #AAD8CC;
    display: flex;
    justify-content: space-between;
    padding: 0 24rpx;
    align-items: center;
    font-weight: 500;
    font-size: 26rpx;
    color: #00926B;
    margin: 32rpx 0;
    box-sizing: border-box;
    .surrender{

    }
  }
  .guidanceTwoTitle{
    width: 686rpx;
    height: 60rpx;
    line-height: 60rpx;
    background: #FFEEE6;
    border-radius: 8rpx;
    padding: 0 24rpx;
    font-weight: 500;
    font-size: 26rpx;
    color: #FF5500;
    box-sizing: border-box;
    margin-top: 12rpx;
    border: 1rpx solid #F7D4C4;
  }
.mask{
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.6);
  position: fixed;
  left: 0;
  top: 0;
}
.headerTap {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .cencelTitle {
    font-weight: 600;
    font-size: 36rpx;
    color: #1d2029;
    display: flex;
    align-items: center;
    .cencelTitleIcon {
      width: 40rpx;
      height: 40rpx;
      margin-right: 12rpx;
    }
    .surplus{
      font-weight: bold;
      font-size: 32rpx;
      color: #4E5569;
      margin-left: 12rpx;
      margin-right: 6rpx;
    }
    .serplusTime{
      font-weight: 800;
      font-size: 36rpx;
      color: #FF5500;
    }
  }
}
.tapClue {
  width: 100%;
  background: #e0f4ef;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  border: 1rpx solid #aad8cc;
  line-height: 60rpx;
  font-weight: 500;
  font-size: 26rpx;
  color: #00926b;
  padding-left: 24rpx;
  margin-top: 16rpx;
  box-sizing: border-box;
}
.orderInfo {
  .orderTitle {
    margin-bottom: 16rpx;
    font-weight: 600;
    font-size: 32rpx;
    color: #2d2f38;
  }
  .timeTitle {
    font-weight: 400;
    font-size: 26rpx;
    color: #2d2f38;
  }
  .orderIdTitle {
    font-weight: 400;
    font-size: 26rpx;
    color: #2d2f38;
  }
  .orderValue {
    margin-bottom: 16rpx;
    display: flex;
    justify-content: space-between;
    font-weight: 400;
    font-size: 26rpx;
    color: #6f7281;
    .copy {
      margin-left: 8rpx;
      font-weight: 400;
      font-size: 26rpx;
      color: #316EAB;
    }
  }
}
.orderMap{
    padding-bottom: 32rpx;
    .orderInfo{
      padding-bottom: 32rpx;
      border-bottom: 2rpx solid #EAEBF0;
    }
    .serverNameT{
      margin: 32rpx 0;
      display: flex;
      justify-content:space-between;
    .serverNameTitle{
      font-weight: 600;
      font-size: 32rpx;
      color: #1D2029;
    }
    .payPrice{
      font-weight: 500;
      font-size: 36rpx;
      color: #FF5500;
    }
  }
    .orderItem{
    width: 100%;
    font-weight: 400;
    font-size: 26rpx;
    color: #4E5569;
    display: flex;
    justify-content: space-between;
    margin-bottom: 16rpx;
    .orderTitle{
      font-weight: 400;
      font-size: 26rpx;
      color: #1D2029;
    }
    .payPrice{
      font-weight: 500;
      font-size: 26rpx;
      color: #FF5500;
    }
    .copy{
      font-weight: 400;
      font-size: 26rpx;
      color: #316EAB;
      margin-left: 8rpx;
    }
  }
  }
.serverTitle {
  display: flex;
  .paySign {
    font-weight: 400;
    font-size: 22rpx;
    color: #ff5500;
  }
  .payNums {
    font-weight: 500;
    font-size: 36rpx;
    color: #ff5500;
  }
  .serverIcon {
    width: 112rpx;
    height: 112rpx;
    background: #d8d8d8;
    border-radius: 12rpx;
    border: 1rpx solid #d9dbe0;
    margin-right: 20rpx;
    flex-shrink: 0;
  }
  .serviceName {
    font-weight: 500;
    font-size: 32rpx;
    color: #1d2029;
    display: flex;
    flex-wrap: wrap;
    .serviceName {
      font-weight: 500;
      font-size: 32rpx;
      color: #1d2029;
    }
    .serverTime {
      font-weight: 400;
      font-size: 24rpx;
      color: #1d2029;
      width: 100%;
      margin-top: 8rpx;
      .tabTitle {
        font-weight: 400;
        font-size: 24rpx;
        color: #4e5569;
      }
    }
    .employee {
      font-weight: 400;
      font-size: 22rpx;
      color: #1d2029;
      display: flex;
      align-items: center;
      .avatar {
        width: 32rpx;
        height: 32rpx;
        margin-left: 20rpx;
        border-radius: 50%;
        margin-right: 8rpx;
      }
    }
    .signal {
      font-weight: 400;
      font-size: 22rpx;
      color: #ff5500;
    }
    .serverNum {
      font-weight: 500;
      font-size: 36rpx;
      color: #ff5500;
    }
    .tag {
      font-weight: 400;
      font-size: 20rpx;
      color: #868c9c;
    }
  }
}
.guidanceBox {
  width: 100vw;
  padding: 0 32rpx;
  box-sizing: border-box;
  height: calc(100vh - 274rpx - 350rpx);
  overflow: scroll;
}
.guidanceCard {
  width: 686rpx;
  background: #ffffff;
  border-radius: 16rpx;
  margin-top: 32rpx;
  padding: 20rpx 24rpx;
  box-sizing: border-box;
  .serviceName {
    font-weight: 500;
    font-size: 32rpx;
    color: #1d2029;
    .signal {
      font-weight: 400;
      font-size: 22rpx;
      color: #ff5500;
    }
    .serverNum {
      font-weight: 500;
      font-size: 36rpx;
      color: #ff5500;
    }
    .tag {
      font-weight: 400;
      font-size: 20rpx;
      color: #868c9c;
    }
  }
  .headerTab {
    width: 686rpx;
    height: 192rpx;
    background: #ffffff;
    border-radius: 16rpx;
    padding: 24rpx;
    box-sizing: border-box;
    display: flex;
    .serverIcon {
      width: 112rpx;
      height: 112rpx;
      background: #d8d8d8;
      border-radius: 12rpx;
      border: 1rpx solid #d9dbe0;
      margin-right: 20rpx;
    }
    .changeServer {
      width: 148rpx;
      height: 52rpx;
      background: #ffffff;
      border-radius: 36rpx;
      border: 1rpx solid #d9dbe0;
      margin-left: auto;
    }
  }
}
.selectInsurance {
  background: linear-gradient(133deg, #ffffff 0%, #e0f4ef 100%) !important;
  .InsuranceInfo {
    border: 2rpx solid #9cebd7 !important;
  }
}
.Insurance {
  width: 100%;
  height: 268rpx;
  background: linear-gradient(133deg, #ffffff 0%, #ffebcb 100%);
  border-radius: 16rpx 16rpx 16rpx 16rpx;
  margin-top: 24rpx;
  padding: 20rpx;
  box-sizing: border-box;
  .shake{
    animation: shake 0.5s;
    animation-iteration-count: infinite;
  }
  // 写的简短一点
  @keyframes shake {
    0% {
      transform: translate(1px, 1px) rotate(0deg);
    }
    50% {
      transform: translate(-1px, -2px) rotate(-1deg);
    }
    100% {
      transform: translate(1px, -1px) rotate(1deg);
    }
  }
  .notice{
    font-weight: 500;
    font-size: 24rpx;
    color: #777777;
    display: flex;
    align-items: center;
    margin-top: 8rpx;
    .selectorButton {
      width: 32rpx;
      height: 32rpx;
      margin-top: auto;
      margin-right: 8rpx;
    }
    .specification{
      color: #1687F7;
    }
  }
  .InsuranceInfo {
    width: 100%;
    height: 192rpx;
    background: #ffffff;
    border-radius: 8rpx 8rpx 8rpx 8rpx;
    border: 2rpx solid #ffe3b6;
    padding: 16rpx;
    box-sizing: border-box;
    .InsuranceContent {
      display: flex;
      align-items: flex-end;
      .Group {
        width: 120rpx;
        height: 120rpx;
      }
      .InsuranceInfoMiddle {
        margin-left: 16rpx;
        .InsuranceContentTitle {
          display: flex;
          align-items: center;
          .title {
            font-weight: 800;
            font-size: 32rpx;
            color: #1d2029;
            margin-right: 8rpx;
          }
          .miniTitle {
            height: 28rpx;
            border-radius: 4rpx 4rpx 4rpx 4rpx;
            border: 1rpx solid #7777777a;
            font-weight: 400;
            font-size: 22rpx;
            color: #777777;
            display: flex;
            align-items: center;
            padding: 0 6rpx;
            box-sizing: border-box;
          }
          .right_arrow {
            width: 28rpx;
            height: 28rpx;
          }
        }
        .InsurancePrompt {
          font-weight: 500;
          font-size: 22rpx;
          color: #ff5500;
          visibility: hidden;
        }
        .pay {
          font-weight: 500;
          font-size: 24rpx;
          color: #ff5500;
          .dollarSign {
            font-size: 24rpx;
          }
          .payNum {
            font-size: 34rpx;
          }
          .everyone {
            font-size: 32rpx;
          }
          .man {
            font-size: 24rpx;
          }
        }
      }
      .selectBox{
        height: 120rpx;
        flex: 1;
        padding-left: 32rpx;
        display: flex;
        align-items: flex-end;
      }
      .selectorButton {
        width: 32rpx;
        height: 32rpx;
      }
    }
    .describe {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      font-weight: 400;
      font-size: 22rpx;
      color: #4E5569;
      margin-top: 8rpx;
      .Check {
        width: 32rpx;
        height: 32rpx;
        &:first-child {
          margin: 0 12rpx 0 0 !important;
        }
        &:not(:first-child){
          margin: 0 8rpx 0 20rpx;
        }
      }
    }
  }
}
  .DetailsBox{
    z-index: 2;
    position: fixed;
    bottom: -378rpx;
    left: 0;
    width: 750rpx;
    height: 378rpx;
    background: #F4F6FA;
    border-radius: 24rpx 24rpx 0rpx 0rpx;
    transition: all 0.1s ease-out;
    padding: 32rpx 32rpx 48rpx;
    box-sizing: border-box;
    .confirmTitle{
      font-weight: 500;
      font-size: 34rpx;
      color: #1D2029;
      text-align: center;
      position: relative;
      .iconClose{
        width: 32rpx;
        height: 32rpx;
        position: absolute;
        right: 0;
        top: 6rpx;
      }
    }
    .DetailsContent{
      width: 100%;
      background: #FFFFFF;
      border-radius: 16rpx 16rpx 16rpx 16rpx;
      padding: 50rpx 20rpx 16rpx;
      box-sizing: border-box;
      .DetailsLine{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 16rpx;
      }
      .lineTitle{
        font-weight: 500;
        font-size: 34rpx;
        color: #1D2029;
      }
      .lineSign{
        font-weight: 400;
        font-size: 22rpx;
        color: #FF5500;
      }
      .totalPrice{
        font-weight: 500;
        font-size: 36rpx;
        color: #FF5500;
      }
    }
  }
  .showDetailsBox{
    bottom: 270rpx;
  }
  .bottom{
    position: fixed;
    bottom: 0rpx;
    left: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
  }
  .bottomButtonMap{
      display: flex;
      justify-content: space-between;
      margin: 22rpx 0 22rpx 0;
      padding: 0 32rpx;
      box-sizing: border-box;
      width: 100%;
    .buttonItem{
      width: 334rpx;
      height: 88rpx;
      background: #FFFFFF;
      border-radius: 44rpx 44rpx 44rpx 44rpx;
      border: 2rpx solid #D9DBE0;
      font-weight: 400;
      font-size: 32rpx;
      color: #1D2029;
      text-align: center;
      line-height: 88rpx;
    }
  }
  .bottomClearBtn{
    width: 684rpx;
    height: 88rpx;
    background: #FFFFFF;
    border-radius: 44rpx;
    border: 1rpx solid #D9DBE0;
    font-weight: 400;
    font-size: 32rpx;
    color: #4E5569;
    text-align: center;
    line-height: 88rpx;
    margin: 32rpx auto;
  }
.buttonMap {
  display: flex;
  text-align: center;
  margin-top: 32rpx;
  justify-content: space-between;
  background: white;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0 32rpx 68rpx;
  box-sizing: border-box;
  flex-wrap: wrap;
  z-index: 3;
  .agreement{
    transform: translateX(-32rpx);
    margin-bottom: 5px;
  }
  .Detail {
    width: 100%;
    height: 90rpx;
    background: #ffffff;
    border-radius: 0rpx 0rpx 0rpx 0rpx;
    box-sizing: border-box;
    align-items: center;
    display: flex;
    justify-content: space-between;
    position: relative;
    margin-bottom: 24rpx;
    .pays {
      font-weight: 500;
      font-size: 36rpx;
      color: #ff5500;
    }
    .showBtnUpDown{
      .right_arrow{
        transform: rotate(270deg) !important;
      }
    }
    .showBtn {
      font-weight: 500;
      font-size: 24rpx;
      color: #777777;
      display: flex;
      align-items: center;
      .right_arrow {
        transition: all 0.1s ease-out;
        width: 32rpx;
        height: 32rpx;
        margin-left: 8rpx;
        transform: rotate(90deg);
      }
    }
    .line {
      background-color: #e6e6e6;
      position: absolute;
      bottom: -1rpx;
      left: -32rpx;
      height: 2rpx;
      width: 100vw;
    }
  }
  .clearBtn {
    width: 196rpx;
    height: 88rpx;
    background: #ffffff;
    border-radius: 44rpx;
    border: 1rpx solid #d9dbe0;
    font-weight: 400;
    font-size: 32rpx;
    line-height: 88rpx;
    color: #1d2029;
  }
  .inLinePayBtn {
    width: 458rpx;
    height: 88rpx;
    line-height: 88rpx;
    background: #00b484;
    border-radius: 44rpx;
    font-weight: 500;
    font-size: 32rpx;
    color: #ffffff;
  }
}
.guidanceTitle {
  font-weight: 500;
  font-size: 36rpx;
  color: #1d2029;
}
.guidanceTwoTitle {
  font-weight: 400;
  font-size: 26rpx;
  color: #868c9c;
}
.guidanceTwoTitle {
  width: 686rpx;
  height: 60rpx;
  line-height: 60rpx;
  background: #ffeee6;
  border-radius: 8rpx;
  padding: 0 24rpx;
  font-weight: 500;
  font-size: 26rpx;
  color: #ff5500;
  box-sizing: border-box;
  margin-top: 12rpx;
  border: 1rpx solid #f7d4c4;
}
.underInfo{
  .underInfoHeader{
    display: flex;
    align-items: center;
    justify-content: space-between;
    .filled{
      display: flex;
      align-items: center;
      font-weight: 400;
      font-size: 26rpx;
      color: #777777;
      .right_arrow{
        width: 32rpx;
        height: 32rpx;
      }
    }
  }
  .underInfoTitle{
    font-weight: 600;
    font-size: 32rpx;
    color: #1D2029;
  }
  .underInfoBox{
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 400;
    font-size: 26rpx;
    color: #1D2029;
    margin-top: 24rpx;
    .changeInfo{
      display: flex;
      align-items: center;
    }
    .entering{
      width: 28rpx;
      height: 28rpx;
      margin-left: 4rpx;
    }
  }
}
</style>
