<template>
  <view class='my' :class="'my-' + skinColor">
    <view class="my-data" :style="{'background-image':'url('+ file_ctx +'static/image/business/accompany-doctor/icon-accompany-my-bg.png)','background-size': '100%'}">
      <view :style="'height:' + statusBarHeight + 'px;'"></view>
      <view class="top-nav">
        <view class="top-nav-c">我的</view>
      </view>
      <view class="my-head">
        <view class="my-head-l">
          <view class="profile">
            <default-img
              style="width: 112rpx;height: 112rpx;display: inline-block;"
              :config="config.avatar"
              :cData="infoObj.avatarUrl"
              :cName="infoObj?infoObj.name:''"
              class="role-image"
            />
          </view>
          <view class="name" v-if="isLogin">{{ infoObj.name || '暂无名称'}}</view>
          <view class="name" v-else>请先登录</view>
        </view>
        <view class="my-head-r" @click="handleClickUpdateUser">修改资料</view>
      </view>
    </view>
    <view class="content-wrapper">
      <view class="my-card">
        <view class="my-card-main">
          <view class="li l76" @tap="$navto.push('AccompanyPersonalMyPosts', {orderIndex: 1})">
            <view class="number">{{ dataStatistics.postMessageCount || 0 }}</view>
            <view class="text">
              我的帖子
            </view>
          </view>
          <view class="li l74" @tap="$navto.push('AccompanyPersonalMyComment', {orderIndex: 2})">
            <view class="number">{{ dataStatistics.commitCount || 0 }}</view>
            <view class="text">
              我的评论
            </view>
          </view>
          <view class="li l76" @tap="$navto.push('AccompanyPersonalMyCollect', {orderIndex: 3})">
            <view class="number">{{ dataStatistics.collectionCount || 0 }}</view>
            <view class="text">
              我的收藏
            </view>
          </view>
          <view class="li" @tap="$navto.push('AccompanyPersonalMyLike', {orderIndex: 4})">
            <view class="number">{{ dataStatistics.likeCount || 0 }}</view>
            <view class="text">
              我的点赞
            </view>
          </view>
        </view>
      </view>

      <!-- 服务商功能快捷入口 -->
      <view class="provider-functions" v-if="isLogin && hasProvider && isCurrentUserProvider">
        <view class="function-item" v-for="(item, index) in providerFunctions" :key="index" @click="handleClickJump(item)">
          <view class="function-name" :class="{'fix-width': item.routerName === 'storeQrCode'}">{{item.name}}</view>
          <view class="function-icon">
            <image :src="file_ctx + item.icon" mode="aspectFit"></image>
          </view>
        </view>
      </view>

      <!-- 服务商数据概览 -->
      <view class="provider-overview" v-if="isLogin && hasProvider && isCurrentUserProvider">
        <view class="overview-header">
          <view class="left-content" @click="$navto.push('ProviderDataPanel')">
            <view class="title">今日数据概览<image :src="file_ctx + 'static/image/business/accompany-doctor/icon-qverview-left.png'" mode="aspectFit"></image></view>
            <view class="update-time">数据时间：{{providerStats.updateTime || '未更新'}}</view>
          </view>
          <view class="search-icon">
            <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-qverview-search.png'" mode="aspectFit"></image>
          </view>
        </view>
        <view class="overview-content">
          <view class="overview-content-item">
            <view class="stat-item">
              <view class="stat-label">订单数</view>
              <view class="stat-value">{{providerStats.orderCount}}</view>
              <view class="stat-trend" :class="{'up': providerStats.orderTrend.includes('+'), 'down': providerStats.orderTrend.includes('-')}" @click="$navto.push('DataPanel')">
                {{providerStats.orderTrend}}
                <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-qverview-left.png'" mode="aspectFit"></image>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-label">帖子阅读次数</view>
              <view class="stat-value">{{providerStats.postViewCount}}</view>
              <view class="stat-trend" :class="{'up': providerStats.postViewTrend.includes('+'), 'down': providerStats.postViewTrend.includes('-')}" @click="$navto.push('DataPanel')">
                {{providerStats.postViewTrend}}
                <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-qverview-left.png'" mode="aspectFit"></image>
              </view>
            </view>
            <view class="stat-item">
              <view class="stat-label">收益</view>
              <view class="stat-value">¥ {{providerStats.income}}</view>
              <view class="stat-trend" :class="{'up': providerStats.incomeTrend.includes('+'), 'down': providerStats.incomeTrend.includes('-')}" @click="$navto.push('DataPanel')">
                {{providerStats.incomeTrend}}
                <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-qverview-left.png'" mode="aspectFit"></image>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 陪诊师工作台入口 -->
      <view class="accompany-workbench" v-if="hasEmployee && isCurrentUserEmployee">
        <view class="accompany-workbench-item" @click="$navto.push('accompanyIndex')">
            <view class="workbench-left">
              <view class="workbench-icon">
                <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-workbench.png'" mode="aspectFit"></image>
              </view>
              <view class="workbench-info">
                <view class="workbench-title">陪诊师工作台</view>
                <view class="workbench-desc">全流程订单追踪与标准化培训服务</view>
              </view>
            </view>
            <view class="workbench-right">
              <image :src="file_ctx + 'static/image/business/accompany-doctor/icon-qverview-left.png'" mode="aspectFit"></image>
            </view>
          </view>
      </view>

      <!-- 菜单内容区域 -->
      <view class="my-content">
        <view v-for="(section, sectionIndex) in sectionList" :key="sectionIndex" class="section-block" :class="{'logout-block': section.type === 'logout'}">
          <view v-if="section.title" class="section-title">
            <view class="title-indicator"></view>
            <text>{{section.title}}</text>
          </view>
          <view v-if="section.type !== 'logout'" class="section-items">
            <view v-for="(item, itemIndex) in section.items" :key="itemIndex" class="section-item" @click="handleClickJump(item)">
              <view class="icon-wrapper">
                <image class="item-icon" :src="file_ctx + item.url"></image>
              </view>
              <view class="item-text">{{item.name}}</view>
              <view class="badge" v-if="item.routerName === 'AccompanyNewsReply' || item.routerName === 'AccompanyNewsLikeCollect'">{{item.badgeCount || 0}}</view>
            </view>
          </view>
          <view v-else-if="isLogin" class="logout-item" @click="handleClickJump(loginOutItem)">
            <view class="icon-wrapper">
              <image class="item-icon" :src="file_ctx + loginOutItem.url"></image>
            </view>
            <view class="item-text">{{loginOutItem.name}}</view>
          </view>
        </view>
      </view>

      <view class="isNoLogin" v-if="!isLogin" @click="gotoLogin">登陆查看更多</view>
      <changeProviderMain ref="changeProviderMain"></changeProviderMain>
    </view>
  </view>
</template>

<script>
  import changeProviderMain from '@/modules/accompany-doctor/home/<USER>/my/components/changeProviderMain.vue'
  console.log('changeProviderMain',changeProviderMain);
  
  import { mapState } from 'vuex'
  import defaultImg from '@/components/basics/default-avatar/index'
  // import common from '@/common/util/main'
  import serverOptions from '@/config/env/options'
  export default {
    props: {
      skinColor: {
        type: String,
        default: ''
      }
    },
    components: {
      defaultImg,
      changeProviderMain
    },
    data(){
      return{
        file_ctx:this.file_ctx,
        $constant: this.$constant,
        $static_ctx: this.$static_ctx,
        statusBarHeight: 0,
        menuData: {
          basic: {
            title: '基础应用',
            type: 'basic',
            items: [
              // {showFlag:false,showType:['hasProvider','hasEmployee'],name:'我的余额',url:'static/image/business/accompany-doctor/icon-accompany-my-cards.png',routerName:'AccompanyPersonalMyWallet'},
              // {showFlag:true,showType:['hasProvider','hasEmployee'],name:'我的发布',url:'static/image/business/accompany-doctor/icon-accompany-my-release.png',routerName:'AccompanyPersonalMyPosts'},
              {showFlag:true,showType:['all'],name:'我的套餐',url:'static/image/business/accompany-doctor/icon-accompany-my-combo.png',routerName:'myComboIndex'},
              {showFlag:true,showType:['hasProvider'],name:'服务商管理',url:'static/image/business/accompany-doctor/icon-accompany-my-facilitator.png',routerName:'ProviderWorkBench'},
              // {showFlag:true,showType:['hasEmployee'],name:'陪诊师入口',url:'static/image/business/accompany-doctor/icon-accompany-my-accompany.png',routerName:'accompanyIndex'},
              // {showFlag:false,showType:['all'],name:'陪诊记录',url:'static/image/business/hulu-v2/icon-accompanying-records.png',routerName:'accompanyOrderIdList'},
              {showFlag:false,showType:['all'],name:'就诊人档案',url:'static/image/business/accompany-doctor/icon-visitor-profile.png',routerName:'Patient'},
              {showFlag:false,showType:['all'],name:'门诊无忧订单',url:'static/image/business/accompany-doctor/icon-accompany-my-insurance.png',routerName:'insuranceList'},
              {showFlag:false,showType:['hasProvider','hasEmployee'],name:'我的手机',url:'static/image/business/accompany-doctor/icon-accompany-my-phone.png',routerName:'ModifyPhone'},
              {showFlag:false,showType:['alwaysShowUnlessEmployee'],name:'陪诊师申请',url:'static/image/business/accompany-doctor/icon-application.png',routerName:'Application'},
              // {showFlag:false,showType:['all'],name:'门店管理',url:'static/image/business/accompany-doctor/icon-application.png',routerName:'storeManagement'},
              // {showFlag:false,showType:['all'],name:'申请分销',url:'static/image/business/accompany-doctor/consumer.png',routerName:'DistributionApply'},
              {showFlag:false,showType:['alwaysShowUnlessDistributor'],name:'申请分销',url:'static/image/business/accompany-doctor/icon-distribution-management.png',routerName:'applicationDistribution'},
            ]
          },
          distribution: {
            title: '分销应用',
            type: 'distribution',
            items: [
              // {showFlag:false,showType:['hasEmployee','hasIndividual'],name:'分销管理',url:'static/image/business/accompany-doctor/icon-distribution-management.png',routerName:'applicationDistribution'},
              // {showFlag:false,showType:['hasEmployee','hasIndividual'],name:'邀请好友分销',url:'static/image/business/accompany-doctor/icon-friend-distribution.png',routerName:'inviteFriends'},
              // {showFlag:false,showType:['all'],name:'邀请好友分销',url:'static/image/business/accompany-doctor/icon-friend-distribution.png',routerName:'inviteFriends'},
              {showFlag:false,showType:['isDistributor'],name:'分销中心',url:'static/image/business/accompany-doctor/consumer.png',routerName:'Distribution'},
              {showFlag:false,showType:['isDistributor'],name:'分销海报',url: 'static/image/business/hulu-v2/icon-distributionPoster.png',routerName:'DistributionPoster'}
            ]
          },
          notification: {
            title: '消息通知',
            type: 'notification',
            items: [
              {showFlag:true,showType:['all'],name:'我的回复',url:'static/image/business/accompany-doctor/icon-accompany-my-message.png',routerName:'AccompanyNewsReply',badgeKey:'commitCount'},
              {showFlag:true,showType:['all'],name:'点赞收藏消息',url:'static/image/business/accompany-doctor/icon-accompany-my-like.png',routerName:'AccompanyNewsLikeCollect',badgeKey:'likeCount'},
            ]
          }
        },
        // 服务商数据概览
        providerStats: {
          updateTime: '',
          orderCount: 0,
          postViewCount: 0,
          income: 0,
          orderTrend: '+0%',
          postViewTrend: '+0%',
          incomeTrend: '+0%'
        },
        // 服务商功能模块
        providerFunctions: [
          {name: '门店码', icon: 'static/image/business/accompany-doctor/icon-store-code.png', routerName: 'storeQrCode'},
          {name: '门店管理', icon: 'static/image/business/accompany-doctor/icon-store-management.png', routerName: 'storeManagement'},
          {name: '快速创单', icon: 'static/image/business/accompany-doctor/icon-quick-order.png', routerName: 'ProviderOrderCenter'}
        ],
        loginOutItem:{showFlag:false,showType:['all'],name:'退出登录',url:'static/image/business/accompany-doctor/icon-accompany-my-exit.png',routerName:'loginOut'},
        config: {
          avatar: {
            widthHeightAuto: true,
            itemClass: {
              width: '112rpx',
              height: '112rpx',
              display: 'inline-block',
            }
          }
        },
        dataStatistics:{},
        // 是否是陪诊师
        hasEmployee:false,
        // 是否是服务商
        hasProvider:false,
        // 是否是个人消费者
        hasIndividual:false,
        // 添加sectionList到data中
        sectionList: [],
        // 保存服务商ID和陪诊师ID
        _providerId: null,
        isDistributor: false
      }
    },
    computed: {
      ...mapState('user', {
        accountId: state => state.accountId,
        fansRecord: state => state.fansRecord,
        isLogin: state => state.isLogin
      }),
      infoObj() {
        if(!this.isLogin) return {};
        return {
          name: this.fansRecord.nickName,
          avatarUrl: this.fansRecord.headPath ? this.fansRecord.headPath : this.defaultAvatar,
          lightLogoPath: this.fansRecord.lightLogoPath
        }
      },
      // 当前系统服务商ID从serverOptions中获取
      currentProviderId() {
        return serverOptions.providerId;
      },
      // 判断用户是否为当前系统的服务商
      isCurrentUserProvider() {
        return this.hasProvider && String(this._providerId) === String(this.currentProviderId);
      },
      // 判断用户是否为当前系统的陪诊师
      isCurrentUserEmployee() {
        return this.hasEmployee && String(this._providerId) === String(this.currentProviderId);
      }
    },
    async onLoad(){
      console.log('执行');
      const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);
      // 只有当用户ID存在时才调用接口
      if (codeUserInfo && codeUserInfo.id) {
        let { data: providerId } = await this.$api.accompanyDoctor.accompanyproviderUserProvider({ userId: codeUserInfo.id });
        this._providerId = providerId; // 保存以便在computed中使用
      }
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
      // 初始化空菜单
      this.sectionList = [];
      this.initUserInfo();
    },
    async mounted(){
      this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
      const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);
      if (codeUserInfo?.id) {
        let { data: { hasEmployee, hasProvider } } = await this.$api.accompanyDoctor.accompanybookUserRole({ userId: codeUserInfo.id });
        let { data: providerId } = await this.$api.accompanyDoctor.accompanyproviderUserProvider({ userId: codeUserInfo.id });

        // 保存providerId以便在计算属性中使用
        this._providerId = providerId;
        this.getCommoncollectlikesGetMyStatistics();
        // 获取未读消息数量
        this.getUnreadMessageCount();
        this.$common.setKeyVal('user', 'isUserRole', { hasEmployee, hasProvider }, true);
        this.hasEmployee = hasEmployee;
        this.hasProvider = hasProvider;

        if (!hasEmployee && !hasProvider) this.hasIndividual = true;

        // 如果是服务商，获取服务商数据
        if (hasProvider) {
          this.getProviderStatistics();
        }

        // 判断用户是否为分销员
        try {
          // 获取分销员信息
          const { data: distributorInfo } = await this.$api.distribution.accompanydistributorQueryOneByUserId({
            userId: codeUserInfo.id,
            providerId: serverOptions.providerId
          });

          // 确定是否是分销员
          const isDistributor = !!(distributorInfo && distributorInfo.id);

          if (isDistributor) {
            // 如果是分销员，显示分销应用模块
            this.menuData.distribution.items.forEach(item => {
              // 设置为所有人可见
              item.showType = ['all'];
            });

            // 隐藏申请分销
            const applicationDistributionIndex = this.menuData.basic.items.findIndex(item => item.routerName === 'applicationDistribution');
            if (applicationDistributionIndex !== -1) {
              // 将申请分销项从菜单中移除
              this.menuData.basic.items.splice(applicationDistributionIndex, 1);
            }
          } else {
            // 如果不是分销员，直接删除整个分销应用模块
            delete this.menuData.distribution;
            // 显示申请分销
            this.menuData.basic.items.forEach(item => {
              if (item.routerName === 'applicationDistribution') {
                item.showType = ['all'];
              }
            });
          }
        } catch (error) {
          // 发生错误时，默认不是分销员
          delete this.menuData.distribution;

          this.menuData.basic.items.forEach(item => {
            if (item.routerName === 'applicationDistribution') {
              item.showType = ['all'];
            }
          });
        }

        // 用户权限获取后，重新生成菜单数据
        this.generateSectionList();
      }
    },
    methods: {
      async accompanymultprovideruserJudgeUser(result){
        const userId = serverOptions.getUserId(this);
        let {data:JudgeUser} = await this.$api.accompanyDoctor.accompanymultprovideruserJudgeUser({userId})
        JudgeUser && result[0].items.push({name:'云门店切换',runTimeCb:that=>that.$refs.changeProviderMain.open(),url:'static/image/business/accompany-doctor/icon-cloudGateShop.png'})
      },
      // 生成菜单数据
      generateSectionList() {
        const result = [];

        // 遍历所有菜单数据
        Object.keys(this.menuData).forEach(key => {
          const section = this.menuData[key];

          // 未登录时不显示分销应用
          if (!this.isLogin && section.type === 'distribution') {
            return;
          }

          const visibleItems = section.items.filter(item => this.shouldShowItem(item));

          // 处理角标
          const processedItems = visibleItems.map(item => {
            if (item.badgeKey && this.dataStatistics[item.badgeKey]) {
              return {
                ...item,
                badgeCount: this.dataStatistics[item.badgeKey]
              };
            }
            return item;
          });

          // 只有有可见项的区块才添加
          if (processedItems.length > 0) {
            result.push({
              ...section,
              items: processedItems
            });
          }
        });

        // 只在已登录状态下添加退出登录区块
        if (this.isLogin) {
          result.push({ type: 'logout' });
        }
        this.accompanymultprovideruserJudgeUser(result)
        this.sectionList = result;
      },
      shouldShowItem(item) {
        // 特殊处理 alwaysShowUnless...类型的菜单项
        if (item.showType.includes('alwaysShowUnlessEmployee')) {
          if (!this.isLogin) {
            return true; // 未登录时显示
          }
          // 登录后，如果不是陪诊师则显示
          return !(this.hasEmployee && this.isCurrentUserEmployee);
        }

        if (item.showType.includes('alwaysShowUnlessDistributor')) {
          if (!this.isLogin) {
            return true; // 未登录时显示
          }
          // 登录后，如果不是分销员则显示
          return !this.isDistributor;
        }

        // 如果未登录 (且非上述特殊类型)
        if (!this.isLogin) {
          // 对于其他类型，未登录时只显示 showType 包含 'all' 的项
          return item.showType.includes('all');
        }

        // 如果已登录 (处理剩余的类型)
        if (item.showType.includes('all')) {
          return true;
        }
        if (item.showType.includes('isDistributor')) {
          return this.isDistributor;
        }
        // 'isNotDistributor' 类型已由 'alwaysShowUnlessDistributor' 覆盖，此处无需再判断
        if (item.showType.includes('hasProvider')) {
          return this.hasProvider && this.isCurrentUserProvider;
        }
        if (item.showType.includes('hasEmployee')) {
          return this.hasEmployee && this.isCurrentUserEmployee;
        }
        if (item.showType.includes('hasIndividual')) {
          return this.hasIndividual;
        }

        // 如果没有匹配的showType，则默认不显示
        return false;
      },
       async initUserInfo() {
        // 不管是否登录，先生成一次简单菜单数据，确保显示所有'all'类型的菜单项
        this.generateSectionList();

        const codeUserInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);
        if (codeUserInfo && codeUserInfo.id) {
          let { data: { hasEmployee, hasProvider } } = await this.$api.accompanyDoctor.accompanybookUserRole({ userId: codeUserInfo.id });
          let { data: providerId } = await this.$api.accompanyDoctor.accompanyproviderUserProvider({ userId: codeUserInfo.id });
          this._providerId = providerId; // 保存以便在computed中使用
          this.getCommoncollectlikesGetMyStatistics();
          // 获取未读消息数量
          this.getUnreadMessageCount();
          this.$common.setKeyVal('user', 'isUserRole', { hasEmployee, hasProvider }, true);
          this.hasEmployee = hasEmployee;
          this.hasProvider = hasProvider;

          if (!hasEmployee && !hasProvider) this.hasIndividual = true;

          // 如果是服务商且是当前系统的服务商，获取服务商数据
          if (hasProvider && this.isCurrentUserProvider) {
            this.getProviderStatistics();
          }

          // 判断用户是否为分销员
          try {
            // 获取分销员信息
            const { data: distributorInfo } = await this.$api.distribution.accompanydistributorQueryOneByUserId({
              userId: codeUserInfo.id,
              providerId: serverOptions.providerId
            });
            // 设置是否为分销员状态
            this.isDistributor = !!(distributorInfo && distributorInfo.id);
          } catch (error) {
            // 发生错误时，默认不是分销员
            this.isDistributor = false;
          }

          // 确保在所有数据和角色信息获取完成后才重新生成菜单
          this.generateSectionList();
        } else {
          // 用户ID不存在，只显示基本菜单
          this.isDistributor = false; // 确保未登录时isDistributor为false
          this.generateSectionList();
        }
      },
      // 获取未读消息数量
      async getUnreadMessageCount() {
        try {
          const pageStartDate = this.$common.formatDate(new Date(), 'yyyy-MM-dd HH:mm:ss');
          const accountId = this.$common.getKeyVal('user', 'accountId', true);

          const replyParams = {
            current: 1,
            size: 999,
            condition: {
              createTime: pageStartDate,
              accountId: accountId,
              businessType: [3, 4]
            }
          };
          const replyRes = await this.$api.community.noticelogQueryCommentPage(replyParams);
          // console.log('获取我的回复消息数量', replyRes);

          const likeCollectParams = {
            current: 1,
            size: 999,
            condition: {
              createTime: pageStartDate,
              accountId: accountId,
              businessType: [5, 6, 7]
            }
          };
          const likeCollectRes = await this.$api.community.noticelogQueryCommentPage(likeCollectParams);

          // 更新菜单中的消息数量
          this.menuData.notification.items.forEach(item => {
            if (item.routerName === 'AccompanyNewsReply') {
              // 设置我的回复数量
              item.badgeCount = (replyRes.data && replyRes.data.total) ? replyRes.data.total : 0;
            } else if (item.routerName === 'AccompanyNewsLikeCollect') {
              // 设置点赞收藏消息数量
              item.badgeCount = (likeCollectRes.data && likeCollectRes.data.total) ? likeCollectRes.data.total : 0;
            }
          });

          // 更新消息数量后重新生成菜单
          this.generateSectionList();
        } catch (error) {
          console.error('获取消息数量失败', error);
        }
      },
      // 获取服务商统计数据
      async getProviderStatistics() {
        try {
          // 使用当前服务商ID而不是从本地存储获取
          const providerId = this._providerId;
          if (!providerId || providerId !== this.currentProviderId) {
            console.error('当前用户不是系统服务商，无法获取统计数据');
            return;
          }

          // 获取日期范围
          const timeMap = this.getDatesOfTodayAndLastWeek();

          if (timeMap.length) {
            // 获取订单统计数据
            const orderParams = {
              startTime: timeMap[0],
              endTime: timeMap[1],
              providerId: providerId
            };
            const orderRes = await this.$api.accompanyDoctor.accompanyBookTotalOrderStatistic(orderParams);

            // 获取帖子阅读统计数据
            const postReadParams = {
              startTime: timeMap[0],
              endTime: timeMap[1],
              providerId: providerId
            };
            const postReadRes = await this.$api.accompanyDoctor.accompanyBookPostReadCountStatistic(postReadParams);

            // 获取收益统计数据
            const incomeParams = {
              startTime: timeMap[0],
              endTime: timeMap[1],
              providerId: providerId
            };
            const incomeRes = await this.$api.accompanyDoctor.accompanyBookIncomeStatistic(incomeParams);

            // 更新页面显示数据
            if (orderRes.data && orderRes.data.length > 0 && postReadRes.data && incomeRes.data) {
              const now = new Date();
              // 处理趋势数据格式
              const formatTrend = (trend) => {
                if (!trend) return '+0%';
                // 如果已经包含+或-，直接返回
                if (trend.includes('+') || trend.includes('-')) return trend;
                // 否则默认添加+号
                return `+${trend}`;
              };

              this.providerStats = {
                updateTime: this.$common.formatDate(now, 'yyyy-MM-dd HH:mm:ss').replace(/-/g, '.'),
                orderCount: orderRes.data[0].count || 0,
                postViewCount: postReadRes.data.count || 0,
                income: incomeRes.data.count || 0,
                orderTrend: formatTrend(orderRes.data[0].yesterdayChain || '0%'),
                postViewTrend: formatTrend(postReadRes.data.yesterdayChain || '0%'),
                incomeTrend: formatTrend(incomeRes.data.yesterdayChain || '0%')
              };

              // 更新统计数据后重新生成菜单
              this.generateSectionList();
            }
          }
        } catch (error) {
          console.error('获取服务商统计数据失败', error);
          // 设置默认值
          const now = new Date();
          this.providerStats = {
            updateTime: this.$common.formatDate(now, 'yyyy-MM-dd HH:mm:ss').replace(/-/g, '.'),
            orderCount: 0,
            postViewCount: 0,
            income: 0,
            orderTrend: '+0%',
            postViewTrend: '+0%',
            incomeTrend: '+0%'
          };
        }
      },

      // 获取当天和上个月第一天的日期范围
      getDatesOfTodayAndLastWeek() {
        const dates = [];
        const today = new Date();
        const lastMonth = new Date(today);
        lastMonth.setMonth(lastMonth.getMonth() - 1);

        if (lastMonth.getMonth() === 11 && today.getMonth() === 0) {
          lastMonth.setFullYear(lastMonth.getFullYear() - 1);
        }

        lastMonth.setDate(1);
        dates.push(this.$common.formatDate(lastMonth, 'yyyy-MM-dd HH:mm:ss'));
        dates.push(this.$common.formatDate(today, 'yyyy-MM-dd HH:mm:ss'));
        return dates;
      },
      handleClickUpdateUser() {
        this.$navto.push('UpdateUserInfo');
      },
      async getCommoncollectlikesGetMyStatistics() {
        const param = {
          accountId: this.accountId
        };
        const res = await this.$api.community.commoncollectlikesGetMyStatistics(param);
        let data = res.data;

        // 处理大数字显示
        ['postMessageCount', 'commitCount', 'collectionCount', 'likeCount'].forEach(key => {
          if (data[key] && data[key] > 9999) {
            data[key] = '9999+';
          }
        });

        this.dataStatistics = data;

        // 重新生成菜单数据
        this.generateSectionList();
      },
      handleClickJump(item) {
        if (item.runTimeCb) return item.runTimeCb(this);
        if (item.routerName === 'loginOut') {
          const that = this;
          uni.showModal({
            content: '是否退出登录？',
            confirmText: '确定',
            cancelText: '取消',
            success: function(data) {
              if (data.confirm) {
                that.$ext.user.loginOut();
                                // 重置组件本地的用户相关数据
                that.dataStatistics = {};
                that.hasEmployee = false;
                that.hasProvider = false;
                that.hasIndividual = false; // 个人消费者状态也应重置
                that._providerId = null;
                that.providerStats = {
                  updateTime: '',
                  orderCount: 0,
                  postViewCount: 0,
                  income: 0,
                  orderTrend: '+0%',
                  postViewTrend: '+0%',
                  incomeTrend: '+0%'
                };
                that.isDistributor = false; // 重置分销员状态
                // 强制刷新当前页面以确保视图更新
                that.$forceUpdate();
                // 重新生成菜单列表以反映未登录状态
                that.generateSectionList();
              }
            }
          });
          return;
        }

        // 添加门店码和门店管理的开发中提示
        if (item.routerName === 'storeQrCode') {
          uni.showToast({
            title: '正在开发中...',
            icon: 'none'
          });
          return;
        }

        // 处理陪诊师申请的特殊逻辑
        if (item.routerName === 'Application') {
          this.handleApplicationClick();
          return;
        }
        item.routerName ? this.$navto.push(item.routerName) : this.callPhoneNumber();
      },
       // 处理陪诊师申请点击事件
       async handleApplicationClick() {
        try {
          // 先获取服务商信息，检查employeeFeeContentButton
          const providerResult = await this.$api.accompanyDoctor.accompanyproviderQueryOne({id: serverOptions.providerId});

          if (!providerResult || providerResult.code !== 0 || !providerResult.data) {
            console.error('获取服务商信息失败');
            // 获取失败时默认直接跳转到申请页面
            this.$navto.push('Application');
            return;
          }

          const providerData = providerResult.data;
          const employeeFeeContentButton = providerData.employeeFeeContentButton || 0;
          const employeeFeeButton = providerData.employeeFeeButton || 0;
          const employeeFee = providerData.employeeFee || 0;
          const employeeFeeContent = providerData.employeeFeeContent || '';

          // 检查用户是否已提交申请
          const userInfo = this.$common.getKeyVal('user', 'codeUserInfo', true);
          let hasApplied = false;

          if (userInfo && userInfo.id) {
            // 调用API查询用户是否已提交申请
            const result = await this.$api.accompanyDoctor.getAccompanyemployeeStatus({
              userId: userInfo.id,
              providerId: serverOptions.providerId
            });

            // 如果有返回数据，说明用户已提交申请
            hasApplied = !!(result && result.code === 0 && result.data);
          }

          // console.log('陪诊师申请检查结果:', {
          //   employeeFeeContentButton,
          //   hasApplied
          // });

          // 根据条件判断跳转目标
          if (hasApplied || employeeFeeContentButton === 0) {
            // 如果用户已提交申请，或者介绍页开关关闭（值为0），则直接跳转到申请页面
            this.$navto.push('Application', {
              employeeFeeButton,
              employeeFee,
              // employeeFeeContent: encodeURIComponent(employeeFeeContent)
            });
          } else {
            // 否则跳转到介绍页面
            this.$navto.push('ApplicationIntro');
          }
        } catch (error) {
          console.error('处理陪诊师申请点击事件失败:', error);
          // 发生错误时默认直接跳转到申请页面
          this.$navto.push('Application');
        }
      },
      callPhoneNumber() {
        const phoneNumber = '1361281111';
        uni.makePhoneCall({
          phoneNumber: phoneNumber,
          success: () => {
            // console.log('拨打电话成功！');
          },
          fail: () => {
            // console.error('拨打电话失败！');
          }
        });
      },
      handleBack() {
        this.$navto.back(1);
      },
      gotoLogin() {
        this.$navto.push('Login');
      }
    }
  }
</script>

<style lang='scss' scoped>
  @import '../style/blueSkin.scss';
.my{
  position: relative;
  background: #F4F6FA;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}
.my-data{
  height: 412rpx;
  width: 100%;
  .top-nav{
    // position: fixed;
    width: calc(100% - 16rpx);
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 40px;
    line-height: 40px;
    padding: 0 16rpx;
    // z-index: 999;
    // padding: 0 32rpx 0 24rpx;
    .top-nav-l{
      display: flex;
      width: 48rpx;
      height: 48rpx;
      image{
        width: 100%;
        height: 100%;
      }
    }
    .top-nav-c{
      flex: 1;
      text-align: center;
      height: 44rpx;
      font-weight: 500;
      font-size: 32rpx;
      color: #2D2F38;
      line-height: 44rpx;
      margin-right: 48rpx;
    }
  }
  .my-head{
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24rpx 0 0 32rpx;
    .my-head-l{
      display: flex;
      align-items: center;
      .profile{
        width: 112rpx;
        height: 112rpx;
        border: 1rpx solid #EAEBF0;
        border-radius: 50%;
        image{
          width: 100%;
          height: 100%;
        }
      }
      .name{
        font-size: 36rpx;
        color: #1F2021;
        margin-left: 24rpx;
      }
    }
    .my-head-r{
      display: flex;
      align-items: center;
      justify-content: center;
      width: 160rpx;
      height: 64rpx;
      font-size: 24rpx;
      color: #1F2021;
      background: rgba(255,255,255,0.79);
      border-radius: 200rpx 0rpx 0rpx 200rpx;
    }
  }
}

/* 内容区域容器 - 使用流式布局 */
.content-wrapper {
  width: 100%;
  margin-top: -62rpx;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  padding-bottom: 150rpx;
}

.my-card{
  padding-bottom: 24rpx;
  width: calc(100% - 64rpx);
  margin: 0 32rpx;
  z-index: 10;

  .my-card-main{
    display: flex;
    justify-content: space-evenly;
    padding: 0 24rpx;
    .li{
      // width: 100%;
      position: relative;
      width: 96upx;
      .text{
        width: 96upx;
        height: 34rpx;
        font-weight: 400;
        font-size: 24rpx;
        color: #4E5569;
        text-align: center;
      }
      .number{
        font-size: 36rpx;
        color: #1D2029;
        line-height: 50rpx;
        text-align: center;
        margin-bottom: 8upx;
      }
    }
    .li:last-of-type{
      padding-left: 2upx;
      position: relative;
      .border-left{
        width: 2upx;
        height: 70%;
        position: absolute;
        left: 0;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        background: #f5f5f5;
        @include downBoxShadow(-4upx, 0, 20upx, 1, 204, 204, 204);
      }
    }
    .l76{
      margin-right: 76upx;
    }
    .l74{
      margin-right: 74upx;
    }
  }
}

/* 服务商功能入口样式 */
.provider-functions {
  width: calc(100% - 64rpx);
  margin: 24rpx 32rpx;
  display: flex;
  justify-content: space-between;
  z-index: 9;
  gap: 24rpx; // 按钮间距
  .function-item {
    width: 208rpx;
    height: 104rpx;
    background-color: #ffffff;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.05);

    .function-icon {
      width: 64rpx;
      height: 64rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      image {
        width: 56rpx;
        height: 56rpx;
      }
    }

    .function-name {
      font-size: 28rpx;
      color: #333333;
      padding-left: 14rpx;
      font-weight: bold;
      &.fix-width {
        min-width: 100rpx; // 你可以根据四字按钮实际宽度微调
        display: inline-block;
        text-align: left;
      }
    }

    &:nth-child(1) {
      background-color: rgba(25, 229, 185, 1);
      color: #ffffff;
      .function-name {
        color: #ffffff;
      }
    }

    &:nth-child(2) {
      background-color: rgba(254, 119, 37, 0.80);
      color: #ffffff;
      .function-name {
        color: #ffffff;
      }
    }

    &:nth-child(3) {
      background-color: rgba(47, 154, 255, 0.80);
      color: #ffffff;
      .function-name {
        color: #ffffff;
      }
    }
  }
}

/* 服务商数据概览样式 */
.provider-overview {
  width: calc(100% - 64rpx);
  margin: 0 auto;
  margin-bottom: 24rpx;
  background: linear-gradient(to right, #E8FFF9, #E0F5FF);
  border-radius: 16rpx;
  z-index: 8;

  .overview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;

    .left-content {
      flex: 1;

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 8rpx;
        display: flex;
        align-items: center;
        image{
          width: 32rpx;
          height: 32rpx;
          margin-left: 12rpx;
        }
      }

      .update-time {
        font-size: 22rpx;
        color: #999999;
      }
    }

    .search-icon {
      right: 0;
      position: absolute;

      image {
        width: 259rpx;
        height: 120rpx;
      }
    }
  }

  .overview-content {
    display: flex;
    justify-content: space-between;
    border-radius: 12rpx;
    padding: 0 20rpx 20rpx 20rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    .overview-content-item{
      width: 100%;
      display: flex;
      border-radius: 12rpx;
      background-color: #FFFFFF;
    }
    .stat-item {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: baseline;
      padding: 24rpx 0 24rpx 48rpx;
      position: relative;

      .stat-label {
        font-size: 24rpx;
        color: #333333;
        text-align: center;
      }

      .stat-value {
        font-size: 36rpx;
        font-weight: bold;
        color: #333333;
        text-align: center;
      }

      .stat-trend {
        font-size: 24rpx;
        text-align: center;
        display: flex;
        align-items: center;
        &.up {
          color: #07C160;
        }

        &.down {
          color: #FA5151;
        }
      }
      image{
        width: 24rpx;
        height: 24rpx;
        margin-left: 6rpx;
      }
    }
  }
}

/* 陪诊师工作台入口样式 */
.accompany-workbench {
  width: calc(100% - 64rpx);
  margin: 0 auto;
  border-radius: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 7;
  .accompany-workbench-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, rgba(245,255,253,1) 0%, rgba(178,255,238,1) 100%);
    padding: 24rpx;
    border-radius: 16rpx;
  }
  .workbench-left {
    display: flex;
    align-items: center;

    .workbench-icon {
      width: 92rpx;
      height: 92rpx;
      margin-right: 20rpx;

      image {
        width: 100%;
        height: 100%;
      }
    }

    .workbench-info {
      .workbench-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333333;
        margin-bottom: 8rpx;
      }

      .workbench-desc {
        font-size: 24rpx;
        color: #999999;
      }
    }
  }

  .workbench-right {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    image {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.my-content{
  width: calc(100% - 64rpx);
  margin: 24rpx 32rpx;
  z-index: 5;

  .section-block {
    background: #FFFFFF;
    border-radius: 16rpx;
    margin-bottom: 24rpx;
    padding: 24rpx 0 12rpx;

    .section-title {
      display: flex;
      align-items: center;
      font-size: 28rpx;
      color: #333333;
      font-weight: bold;
      font-family: "PingFang SC", "PingFang SC-Bold";
      padding: 0 24rpx 24rpx;
      line-height: 39.2rpx;

      .title-indicator {
        width: 6rpx;
        height: 28rpx;
        background-color: #00B484;
        border-radius: 3rpx;
        margin-right: 12rpx;
      }
    }

    .section-items {
      display: flex;
      flex-wrap: wrap;
      padding: 0;
    }

    .section-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      position: relative;
      margin-bottom: 32rpx;

      .icon-wrapper {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border: none;
        border-radius: 0;
        margin-bottom: 12rpx;
      }

      .item-icon {
        width: 56rpx;
        height: 56rpx;
      }

      .item-text {
        font-size: 26rpx;
        color: #333333;
        text-align: center;
        font-family: "PingFang SC";
      }

      .badge {
        position: absolute;
        top: -10rpx;
        right: 16rpx;
        background-color: #FD5600;
        color: white;
        font-size: 20rpx;
        font-weight: bold;
        padding: 0 8rpx;
        border-radius: 16rpx;
        min-width: 32rpx;
        height: 32rpx;
        line-height: 32rpx;
        text-align: center;
        box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
      }
    }

    .logout-item {
      width: auto;
      padding-left: 24rpx;
    }
  }

  .logout-block {
    padding: 24rpx;
  }

  .logout-item {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0;
    width: 100%;

    .icon-wrapper {
      width: 40rpx;
      height: 40rpx;
      margin-bottom: 0;
      margin-right: 16rpx;
    }

    .item-icon {
      width: 40rpx;
      height: 40rpx;
    }

    .item-text {
      font-size: 28rpx;
      color: #333333;
    }
  }
}

  .isNoLogin{
    position: fixed;
    bottom: 20rpx;
    z-index: 999;
    background-color: #00B484;
    padding: 30rpx;
    border-radius: 30rpx;
    left: 50%;
    color: white;
    transform: translateX(-50%);
  }
</style>

